# 应用模板

这是一个通用的 Angular 项目模板，提供基础项目框架，包括：

1. Angular 基本环境
2. 登录、注册功能以及基于 interceptor 的按需登录功能
3. 版权信息、联系方式等静态、通用内容
4. 基于 Service Worker 的模拟后端

本项目使用 [Angular CLI](https://github.com/angular/angular-cli) 版本 19.2.8 创建。

## 开发服务器

要启动本地开发服务器，请运行：

```bash
ng serve
```

服务器启动后，打开浏览器并导航到 `http://localhost:4200/`。当您修改任何源文件时，应用程序将自动重新加载。

## 代码脚手架

Angular CLI 包含强大的代码脚手架工具。要生成新组件，请运行：

```bash
ng generate component component-name
```

要查看可用生成器（如 `components`、`directives` 或 `pipes`）的完整列表，请运行：

```bash
ng generate --help
```

## 构建

要构建项目，请运行：

```bash
ng build
```

这将编译您的项目并将构建产物存储在 `dist/` 目录中。默认情况下，生产构建会优化您的应用程序以提高性能和速度。

## 运行单元测试

要使用 [Karma](https://karma-runner.github.io) 测试运行器执行单元测试，请使用以下命令：

```bash
ng test
```

## 运行端到端测试

对于端到端 (e2e) 测试，请运行：

```bash
ng e2e
```

Angular CLI 默认不附带端到端测试框架。您可以选择适合您需求的框架。

## 项目结构

```
projects/app-ui/
├── public/                  # 公共资源文件
│   └── service-worker/      # Service Worker 模拟后端实现
├── src/
│   ├── app/
│   │   ├── api/             # API 服务
│   │   ├── core/            # 核心模块（认证、拦截器等）
│   │   ├── features/        # 功能模块（登录、注册等）
│   │   ├── layout/          # 布局组件（导航栏、页脚等）
│   │   └── shared/          # 共享组件和工具
│   ├── environments/        # 环境配置
│   └── styles/              # 全局样式
```

## 模拟后端

本项目使用 Service Worker 实现模拟后端，提供以下功能：

1. 用户认证（登录、注册、验证令牌）
2. 数据存储（使用 IndexedDB）
3. 请求拦截和响应

所有模拟 API 实现都在 `public/service-worker/` 目录中。

## 其他资源

有关使用 Angular CLI 的更多信息，包括详细的命令参考，请访问 [Angular CLI 概述和命令参考](https://angular.dev/tools/cli) 页面。
