# 界面风格指南

## 设计原则

### 简洁性
- 界面应该简洁明了，避免视觉杂乱
- 每个页面应该有明确的焦点和层次结构
- 减少不必要的装饰元素，但保留足够的视觉趣味性

### 一致性
- 在整个应用中保持一致的视觉语言
- 组件、颜色、字体和间距应该遵循统一的规则
- 交互模式应该可预测且一致

### 可用性
- 界面应该易于理解和使用
- 重要功能应该容易被发现
- 提供清晰的视觉反馈和状态指示

### 美观性
- 使用现代、专业的设计美学
- 注重细节，包括对齐、间距和比例
- 创造视觉愉悦感和专业印象

## 颜色系统

### 主色调
- 主色：`#2563eb`（现代蓝色）
- 主色-浅色：`#60a5fa`
- 主色-深色：`#1e40af`

### 辅助色
- 强调色：`#ec4899`（柔和粉色）
- 成功色：`#10b981`
- 警告色：`#f59e0b`
- 错误色：`#ef4444`
- 信息色：`#3b82f6`

### 中性色
- 背景色：`#f9fafb`
- 表面色：`#ffffff`
- 边框色：`#e5e7eb`
- 分隔线：`#f3f4f6`

### 文本颜色
- 主要文本：`#111827`
- 次要文本：`#4b5563`
- 禁用文本：`#9ca3af`
- 反色文本：`#ffffff`

## 排版

### 字体家族
- 标题字体：`var(--font-family-heading)`
- 正文字体：`var(--font-family-base)`
- 等宽字体：`var(--font-family-monospace)`

### 字体大小
- 超大标题：`32px`
- 大标题：`28px`
- 中标题：`22px`
- 小标题：`18px`
- 正文：`16px`
- 小文本：`12px`
- 微小文本：`10px`

### 字体粗细
- 超粗体：`700` (用于主要标题)
- 粗体：`600` (用于次要标题和强调)
- 中等：`500` (用于按钮和重要文本)
- 常规：`400` (用于正文)
- 细体：`300` (用于次要信息)

### 行高
- 紧凑：`1.2`
- 标准：`1.5`
- 宽松：`1.8`

## 间距系统

### 基础间距
- 超小间距：`4px`
- 小间距：`8px`
- 中间距：`16px`
- 大间距：`24px`
- 超大间距：`32px`
- 特大间距：`48px`

### 内边距
- 卡片内边距：`20px`
- 表单元素内边距：`14px`
- 按钮内边距：水平 `20px`，垂直 `10px`

### 外边距
- 组件间距：`24px`
- 段落间距：`16px`
- 区块间距：`40px`

## 边框和圆角

### 边框
- 细边框：`1px solid #e5e7eb`
- 中等边框：`2px solid #d1d5db`
- 强调边框：`2px solid #2563eb`

### 圆角
- 无圆角：`0px` (用于某些方块元素)
- 小圆角：`4px` (用于按钮、输入框等)
- 中圆角：`6px` (用于卡片等)
- 大圆角：`12px` (用于模态框等)
- 圆形：`50%` (用于头像、徽章等)

## 阴影系统

### 阴影层级
- 无阴影：`none`
- 轻微阴影：`0 1px 2px rgba(0, 0, 0, 0.05)`
- 中等阴影：`0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`
- 明显阴影：`0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)`
- 强烈阴影：`0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)`

## 组件样式

### 按钮
- 主要按钮：使用主色背景，白色文本，明显阴影
- 次要按钮：使用白色背景，主色边框和文本，轻微阴影
- 文本按钮：无背景，主色文本，无阴影
- 图标按钮：圆形背景，适当内边距，居中图标
- 悬停效果：增加阴影深度，轻微缩放或背景色变化

### 卡片
- 使用白色背景
- 应用中等圆角
- 使用中等阴影
- 内部元素有一致的内边距
- 可以使用微妙的边框或顶部彩色条带来区分不同类型
- 通过阴影变化来响应用户，不要位移

### 表单元素
- 输入框：轻微阴影，聚焦时显示主色边框
- 下拉菜单：与输入框保持一致的样式
- 复选框和单选按钮：使用主色作为选中状态
- 开关：平滑过渡动画，明确的开关状态指示

### 导航元素
- 侧边栏：使用微妙的分隔线和足够的内边距
- 顶部导航：使用阴影来创造层次感
- 标签页：使用下划线或背景色来指示活动状态
- 面包屑：使用适当的分隔符和颜色区分

### 数据展示
- 表格：使用交替行背景色，轻微的边框
- 列表：项目间使用分隔线或足够的间距
- 统计卡片：突出显示数字，使用图标增强视觉效果
- 图表：使用一致的颜色方案，确保可读性

## 响应式设计

### 断点
- 移动设备：`< 600px`
- 平板设备：`600px - 959px`
- 桌面设备：`960px - 1279px`
- 大屏设备：`≥ 1280px`

### 布局调整
- 移动设备：单列布局，隐藏侧边栏，使用汉堡菜单
- 平板设备：可选择性显示侧边栏，减少内边距
- 桌面设备：显示侧边栏，使用多列布局
- 大屏设备：增加最大宽度限制，避免内容过于分散

### 触摸目标
- 所有可点击元素在移动设备上至少 `48px × 48px`
- 按钮和链接之间有足够间距，避免误触

## 动画与过渡

### 过渡时间
- 快速：`0.15s` (用于按钮反馈等)
- 标准：`0.25s` (用于大多数UI元素)
- 慢速：`0.4s` (用于页面转换等)

### 缓动函数
- 标准：`ease` 或 `cubic-bezier(0.25, 0.1, 0.25, 1)`
- 进入：`ease-out` 或 `cubic-bezier(0, 0, 0.2, 1)`
- 退出：`ease-in` 或 `cubic-bezier(0.4, 0, 1, 1)`
- 强调：`cubic-bezier(0.175, 0.885, 0.32, 1.275)`

### 动画使用原则
- 动画应该有目的，增强用户体验而非分散注意力
- 保持简短和流畅，避免过度使用
- 提供减少动画的选项，考虑可访问性需求

## 图标系统

### 图标风格
- 使用线性或填充风格，但在整个应用中保持一致
- 图标应该简洁明了，避免过于复杂的设计
- 使用 SVG 格式以确保在所有屏幕尺寸下的清晰度

### 图标尺寸
- 小图标：`16px` (用于密集UI或辅助元素)
- 标准图标：`24px` (用于大多数UI元素)
- 大图标：`32px` (用于强调或特殊区域)
- 特大图标：`48px` (用于功能展示或空状态)

### 图标颜色
- 默认使用文本颜色以保持一致性
- 可以使用主色或辅助色来强调特定功能
- 确保足够的对比度，特别是在彩色背景上
