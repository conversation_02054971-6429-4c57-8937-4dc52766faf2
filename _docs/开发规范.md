### 特别注意

- 永远不要自行启动开发服务器，用户会通过 `yarn start` 命令自行启动一个。
- 优先从 IDE 的诊断信息中检查错误，完成一个大的开发任务之前要用 `yarn build` 构建一次，并修正错误。

### 技术栈

- 前端框架：Angular 19.2.0
- UI 组件库：Angular Material
- 应用类型：PWA（Progressive Web Application）
- 响应式设计：自适应桌面设备和移动设备
- 应用类型：纯客户端应用（SPA）
- 前后端通讯协议：RESTful
- 通知接口、实时聊天接口：SSE（Server-Sent Events），用于降低服务器负担
- 尽量不要使用复杂度较低的第三方组件，比如 ngx-markdown，而是自己封装。确实需要的，尽量使用相应的官方组件，不要使用小众组件。
- 总是用 yarn 作为包管理器
- 可以通过 mcp 工具来控制浏览器进行调试。

### 提交规范

遵循 Angular 提交信息的规范。比如 feat(ui): 注册页面

### 代码规范

不要创建 index.ts 等额外层级，直接引用即可。

使用 `@if` 等现代语法，不要用 `*ngIf` 等传统语法。

永远不要用内联式组件（模板、样式），每个组件应该至少包括 *.ts *.svg *.scss（通常使用 `:host {display: block; }` 规则来定义块型组件) 三个部分。

采用以服务为中心的设计，但不要使用 ngRx 等状态管理库，就用 pojo。除了哑组件之外，每个组件都是服务的视图。

在前端使用 Service Worker 模拟后端 API，并且目前是探索实验阶段，因此可以根据需求随时修改接口，以迭代出理想接口。

优先使用 css variables 特性而不是 scss variables 特性。

总是使用字面量类型，永远不要使用枚举类型

API 应该是单一职责的窄接口，不要以页面为单位来设计 API。

API 应该是从业务模型而非视图模型出发的来设计的。

从一开始就拆成小组件，典型的小组件如界面上的一个卡片、一个小型功能区、一个统计图、复杂列表的一个列表项等。

与其他部分没什么联系的小组件可以设计成自给自足的：它自己负责发起 API 与后端交互，自己负责展现。

尽量避免使用 `@ViewChild` 等方式从组件中引用视图。当两个组件有联系时，如果是简单的联系可以用输入属性和输出属性，如果是复杂的联系，应该把它们都简化成同一个服务的两个不同视图，让服务自己管理这些联系。

不要用枚举类型，而要用字面量类型。

所有位于 `mat-*` 按钮下的图标都添加 `matButtonIcon` 属性。

使用 MatIconModule 的标准方式添加 SVG 图标，即：

1. 在 `public/assets/icons` 目录下创建相应名字的 svg 文件。
2. 把这个名字添加到 `src/app/icon-names.ts` 中的 `iconNames` 数组中。
3. 如果删除或改名，这两个地方也必须保持同步。
4. 在需要的地方用如下方式引用：`<mat-icon svgIcon="icon-name" ...></mat-icon>`
