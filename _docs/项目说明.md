# 项目说明

## 项目概述

这是一个前后端分离的 Web 应用项目，包括：

1. **前端 (app-ui)**: Angular 19.2.x 单页应用
2. **后端 (assistant-api)**: Kotlin + Spring Boot REST API 服务

### 主要功能

1. 用户认证（登录、注册、JWT 令牌认证）
2. 用户资料管理
3. 仪表盘数据展示
4. 消息管理
5. 收藏功能
6. 设置管理
7. 关于信息和帮助文档

## 技术栈

### 前端
- Angular 19.2.x
- TypeScript 5.7.x
- Angular Material
- RxJS

### 后端
- Kotlin 1.9.25
- Spring Boot 3.5.0
- Spring Security + JWT
- Spring Data JPA
- PostgreSQL

## 项目结构

```
projects/
├── app-ui/                  # 前端 Angular 应用
│   ├── public/              # 公共资源文件
│   ├── src/
│   │   ├── app/
│   │   │   ├── api/         # API 服务
│   │   │   ├── core/        # 核心模块（认证、拦截器等）
│   │   │   ├── features/    # 功能模块（登录、注册等）
│   │   │   ├── layout/      # 布局组件（导航栏、页脚等）
│   │   │   └── shared/      # 共享组件和工具
│   │   ├── environments/    # 环境配置
│   │   └── styles/          # 全局样式
│   └── ...
└── assistant-api/           # 后端 Spring Boot 应用
    ├── src/main/kotlin/biz/zhizuo/ai/assistant/
    │   ├── config/          # 配置类
    │   ├── controller/      # REST 控制器
    │   ├── dto/             # 数据传输对象
    │   ├── entity/          # JPA 实体类
    │   ├── repository/      # 数据访问层
    │   ├── security/        # 安全配置和 JWT 处理
    │   └── service/         # 业务逻辑层
    ├── src/main/resources/
    │   └── application.yaml # 应用配置
    └── pom.xml              # Maven 配置
```

## 功能模块

### 认证模块

认证模块基于 Spring Security + JWT 实现，提供完整的用户认证功能：

**前端组件：**
- `AuthService`: 提供登录、注册、验证令牌和登出功能
- `AuthGuard`: 保护需要认证的路由
- `AuthInterceptor`: 为 API 请求添加 JWT 令牌
- `HttpErrorInterceptor`: 处理 401 错误，弹出登录对话框

**后端组件：**
- `AuthController`: 认证相关的 REST API
- `AuthService`: 用户认证业务逻辑
- `JwtTokenProvider`: JWT 令牌生成和验证
- `CustomUserDetailsService`: 用户详情服务
- `SecurityConfig`: Spring Security 配置

### 数据持久化

后端使用 Spring Data JPA + PostgreSQL 进行数据持久化：

1. 用户数据（用户、资料、设置）
2. 业务数据（仪表盘、消息、收藏）
3. 内容数据（关于信息、帮助文档）

所有数据在应用启动时通过 `DataInitializer` 自动初始化演示数据。

## 开发指南

### 环境要求

**前端开发：**
- Node.js 18+
- Angular CLI 19+

**后端开发：**
- JDK 17+
- Maven 3.8+
- PostgreSQL 12+

### 启动项目

**1. 启动数据库**
```bash
# 使用 Docker 启动 PostgreSQL
docker run --name postgres -e POSTGRES_DB=voice-mate -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres:12
```

**2. 启动后端服务**
```bash
cd projects/assistant-api
mvn spring-boot:run
```
后端服务将在 http://localhost:18081 启动

**3. 启动前端应用**
```bash
cd projects/app-ui
npm install
ng serve
```
前端应用将在 http://localhost:4200 启动

### 添加新功能

**前端：**
1. 在 `features` 目录下创建新组件
2. 在 `app.routes.ts` 中添加路由配置
3. 在 `api` 目录下添加对应的服务

**后端：**
1. 创建实体类（Entity）
2. 创建数据访问接口（Repository）
3. 创建业务服务（Service）
4. 创建控制器（Controller）
5. 创建 DTO 类

### 添加新组件

```bash
ng generate component features/your-feature/your-component
```

### 添加新服务

```bash
ng generate service shared/services/your-service
```

## 部署指南

### 开发环境

**前端：**
```bash
cd projects/app-ui
ng serve
```

**后端：**
```bash
cd projects/assistant-api
mvn spring-boot:run
```

### 生产环境

**前端：**
```bash
cd projects/app-ui
ng build --configuration production
```

**后端：**
```bash
cd projects/assistant-api
mvn clean package
java -jar target/assistant-api-0.0.1-SNAPSHOT.jar
```

## 注意事项

1. 本项目已从 ServiceWorker 模拟后端迁移到真实的 Spring Boot 后端服务。
2. 前端通过 HTTP 请求与后端 API 进行通信，使用 JWT 进行身份认证。
3. 数据持久化到 PostgreSQL 数据库，应用启动时会自动初始化演示数据。
4. 生产环境需要配置正确的数据库连接和 API 地址。
