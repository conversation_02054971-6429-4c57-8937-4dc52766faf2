package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.AddFavoriteRequest
import biz.zhizuo.ai.assistant.dto.DeleteFavoriteResponse
import biz.zhizuo.ai.assistant.dto.FavoriteResponse
import biz.zhizuo.ai.assistant.service.FavoriteService
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 收藏控制器
 */
@RestController
@RequestMapping("/api/favorites")
@CrossOrigin(origins = ["*"])
class FavoriteController(
    private val favoriteService: FavoriteService
) {

    /**
     * 获取收藏数据
     */
    @GetMapping
    fun getFavorites(): ResponseEntity<FavoriteResponse> {
        return try {
            val response = favoriteService.getFavorites()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }

    /**
     * 添加收藏
     */
    @PostMapping
    fun addFavorite(@Valid @RequestBody request: AddFavoriteRequest): ResponseEntity<Map<String, Any>> {
        return try {
            val favorite = favoriteService.addFavorite(request)
            ResponseEntity.ok(mapOf(
                "message" to "收藏添加成功",
                "favorite" to favorite
            ))
        } catch (e: Exception) {
            ResponseEntity.status(400).body(mapOf(
                "message" to (e.message ?: "收藏添加失败")
            ))
        }
    }

    /**
     * 删除收藏
     */
    @DeleteMapping("/{id}")
    fun deleteFavorite(@PathVariable id: String): ResponseEntity<DeleteFavoriteResponse> {
        return try {
            val response = favoriteService.deleteFavorite(id)
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(400).body(
                DeleteFavoriteResponse(
                    success = false,
                    message = e.message ?: "删除收藏失败"
                )
            )
        }
    }
}
