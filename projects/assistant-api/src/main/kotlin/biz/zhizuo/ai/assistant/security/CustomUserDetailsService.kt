package biz.zhizuo.ai.assistant.security

import biz.zhizuo.ai.assistant.repository.UserRepository
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 自定义用户详情服务
 */
@Service
class CustomUserDetailsService(
    private val userRepository: UserRepository
) : UserDetailsService {

    @Transactional
    override fun loadUserByUsername(usernameOrEmail: String): UserDetails {
        // 用户可以使用用户名或邮箱登录
        val user = userRepository.findByUsername(usernameOrEmail)
            .orElseGet { 
                userRepository.findByEmail(usernameOrEmail)
                    .orElseThrow { 
                        UsernameNotFoundException("用户不存在: $usernameOrEmail") 
                    }
            }

        return UserPrincipal.create(user)
    }

    @Transactional
    fun loadUserById(id: String): UserDetails {
        val user = userRepository.findById(id)
            .orElseThrow { UsernameNotFoundException("用户不存在: $id") }

        return UserPrincipal.create(user)
    }
}
