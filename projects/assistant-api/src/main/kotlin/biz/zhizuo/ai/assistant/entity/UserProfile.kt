package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 用户资料实体
 */
@Entity
@Table(name = "user_profiles")
@EntityListeners(AuditingEntityListener::class)
data class UserProfile(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(name = "user_id", unique = true, nullable = false)
    val userId: String,

    @Column(nullable = false, length = 100)
    val name: String,

    @Column(length = 255)
    val avatar: String? = null,

    @Column(nullable = false, length = 100)
    val email: String,

    @Column(name = "join_date")
    val joinDate: LocalDate? = null,

    @Column(columnDefinition = "TEXT")
    val bio: String? = null,

    @Column(name = "posts_count")
    val postsCount: Int = 0,

    @Column(name = "followers_count")
    val followersCount: Int = 0,

    @Column(name = "following_count")
    val followingCount: Int = 0,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime? = null
)
