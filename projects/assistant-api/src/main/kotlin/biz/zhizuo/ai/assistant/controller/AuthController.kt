package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.service.AuthService
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = ["*"])
class AuthController(
    private val authService: AuthService
) {

    /**
     * 用户登录
     */
    @PostMapping("/login")
    fun login(@Valid @RequestBody request: LoginRequest): ResponseEntity<AuthResponse> {
        return try {
            val response = authService.login(request)
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(null)
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    fun register(@Valid @RequestBody request: RegisterRequest): ResponseEntity<AuthResponse> {
        return try {
            val response = authService.register(request)
            ResponseEntity.status(HttpStatus.CREATED).body(response)
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(null)
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    fun getCurrentUser(): ResponseEntity<UserDto> {
        return try {
            val user = authService.getCurrentUser()
            ResponseEntity.ok(user)
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(null)
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    fun logout(): ResponseEntity<Map<String, Any>> {
        return ResponseEntity.ok(mapOf("success" to true, "message" to "登出成功"))
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    fun changePassword(@Valid @RequestBody request: ChangePasswordRequest): ResponseEntity<Map<String, Any>> {
        return try {
            authService.changePassword(request)
            ResponseEntity.ok(mapOf("success" to true, "message" to "密码修改成功"))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(mapOf("success" to false, "message" to (e.message ?: "密码修改失败")))
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    fun resetPassword(@Valid @RequestBody request: ResetPasswordRequest): ResponseEntity<Map<String, Any>> {
        return try {
            authService.resetPassword(request)
            ResponseEntity.ok(mapOf("success" to true, "message" to "重置密码邮件已发送"))
        } catch (e: UnsupportedOperationException) {
            ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(mapOf("success" to false, "message" to "功能尚未实现"))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(mapOf("success" to false, "message" to (e.message ?: "重置密码失败")))
        }
    }

    /**
     * 更新密码
     */
    @PostMapping("/update-password")
    fun updatePassword(@Valid @RequestBody request: UpdatePasswordRequest): ResponseEntity<Map<String, Any>> {
        return try {
            authService.updatePassword(request)
            ResponseEntity.ok(mapOf("success" to true, "message" to "密码更新成功"))
        } catch (e: UnsupportedOperationException) {
            ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(mapOf("success" to false, "message" to "功能尚未实现"))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(mapOf("success" to false, "message" to (e.message ?: "密码更新失败")))
        }
    }

    /**
     * 获取双因子认证状态
     */
    @GetMapping("/two-factor-status")
    fun getTwoFactorStatus(): ResponseEntity<TwoFactorStatusResponse> {
        return try {
            val response = authService.getTwoFactorStatus()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(null)
        }
    }

    /**
     * 启用双因子认证
     */
    @PostMapping("/enable-two-factor")
    fun enableTwoFactor(@Valid @RequestBody request: EnableTwoFactorRequest): ResponseEntity<Map<String, Any>> {
        return try {
            authService.enableTwoFactor(request)
            ResponseEntity.ok(mapOf("success" to true, "message" to "双因子认证已启用"))
        } catch (e: UnsupportedOperationException) {
            ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(mapOf("success" to false, "message" to "功能尚未实现"))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(mapOf("success" to false, "message" to (e.message ?: "启用双因子认证失败")))
        }
    }

    /**
     * 禁用双因子认证
     */
    @PostMapping("/disable-two-factor")
    fun disableTwoFactor(@Valid @RequestBody request: DisableTwoFactorRequest): ResponseEntity<Map<String, Any>> {
        return try {
            authService.disableTwoFactor(request)
            ResponseEntity.ok(mapOf("success" to true, "message" to "双因子认证已禁用"))
        } catch (e: UnsupportedOperationException) {
            ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(mapOf("success" to false, "message" to "功能尚未实现"))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(mapOf("success" to false, "message" to (e.message ?: "禁用双因子认证失败")))
        }
    }
}
