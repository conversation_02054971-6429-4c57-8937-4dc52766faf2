package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.SettingsResponse
import biz.zhizuo.ai.assistant.dto.UpdateSettingsRequest
import biz.zhizuo.ai.assistant.dto.UserSettingsDto
import biz.zhizuo.ai.assistant.entity.UserSettings
import biz.zhizuo.ai.assistant.repository.UserSettingsRepository
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 设置服务
 */
@Service
@Transactional
class SettingsService(
    private val userSettingsRepository: UserSettingsRepository
) {

    /**
     * 获取用户设置
     */
    @Transactional(readOnly = true)
    fun getSettings(): SettingsResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val settings = userSettingsRepository.findByUserId(userPrincipal.id)
            .orElseGet {
                // 如果没有设置记录，创建默认设置
                val defaultSettings = UserSettings(userId = userPrincipal.id)
                userSettingsRepository.save(defaultSettings)
            }

        val settingsDto = UserSettingsDto(
            id = settings.id,
            userId = settings.userId,
            themeMode = settings.themeMode,
            language = settings.language,
            notificationsEnabled = settings.notificationsEnabled,
            emailNotifications = settings.emailNotifications,
            pushNotifications = settings.pushNotifications,
            privacyProfilePublic = settings.privacyProfilePublic,
            privacyShowEmail = settings.privacyShowEmail,
            privacyShowActivity = settings.privacyShowActivity
        )

        return SettingsResponse(settings = settingsDto)
    }

    /**
     * 更新用户设置
     */
    fun updateSettings(request: UpdateSettingsRequest): SettingsResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val settings = userSettingsRepository.findByUserId(userPrincipal.id)
            .orElseGet {
                UserSettings(userId = userPrincipal.id)
            }

        val updatedSettings = settings.copy(
            themeMode = request.themeMode ?: settings.themeMode,
            language = request.language ?: settings.language,
            notificationsEnabled = request.notificationsEnabled ?: settings.notificationsEnabled,
            emailNotifications = request.emailNotifications ?: settings.emailNotifications,
            pushNotifications = request.pushNotifications ?: settings.pushNotifications,
            privacyProfilePublic = request.privacyProfilePublic ?: settings.privacyProfilePublic,
            privacyShowEmail = request.privacyShowEmail ?: settings.privacyShowEmail,
            privacyShowActivity = request.privacyShowActivity ?: settings.privacyShowActivity
        )

        val savedSettings = userSettingsRepository.save(updatedSettings)

        val settingsDto = UserSettingsDto(
            id = savedSettings.id,
            userId = savedSettings.userId,
            themeMode = savedSettings.themeMode,
            language = savedSettings.language,
            notificationsEnabled = savedSettings.notificationsEnabled,
            emailNotifications = savedSettings.emailNotifications,
            pushNotifications = savedSettings.pushNotifications,
            privacyProfilePublic = savedSettings.privacyProfilePublic,
            privacyShowEmail = savedSettings.privacyShowEmail,
            privacyShowActivity = savedSettings.privacyShowActivity
        )

        return SettingsResponse(settings = settingsDto)
    }
}
