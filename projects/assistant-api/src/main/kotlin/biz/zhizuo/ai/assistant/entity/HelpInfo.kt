package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 帮助信息实体
 */
@Entity
@Table(name = "help_info")
@EntityListeners(AuditingEntityListener::class)
data class HelpInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 50)
    val type: String, // 'faq' 或 'category'

    @Column(nullable = false, length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @Column(length = 100)
    val category: String? = null,

    @Column(length = 50)
    val icon: String? = null,

    @Column(name = "sort_order")
    val sortOrder: Int = 0,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime? = null
)
