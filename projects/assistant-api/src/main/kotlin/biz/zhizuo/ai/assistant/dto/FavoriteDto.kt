package biz.zhizuo.ai.assistant.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * 收藏 DTO
 */
data class FavoriteDto(
    val id: String?,
    val type: String,
    val title: String,
    val description: String?,
    val url: String?,
    val image: String?,
    val author: String?,
    val publishedDate: String?,
    val readTime: String?,
    val duration: String?,
    val viewCount: String?,
    val createdAt: LocalDateTime?
)

/**
 * 收藏响应 DTO
 */
data class FavoriteResponse(
    val articles: List<FavoriteDto>,
    val videos: List<FavoriteDto>
)

/**
 * 添加收藏请求 DTO
 */
data class AddFavoriteRequest(
    @field:NotBlank(message = "类型不能为空")
    val type: String,
    
    @field:NotBlank(message = "标题不能为空")
    @field:Size(max = 200, message = "标题长度不能超过200个字符")
    val title: String,
    
    val description: String?,
    val url: String?,
    val image: String?,
    val author: String?,
    val publishedDate: String?,
    val readTime: String?,
    val duration: String?,
    val viewCount: String?
)

/**
 * 删除收藏响应 DTO
 */
data class DeleteFavoriteResponse(
    val success: Boolean,
    val message: String
)
