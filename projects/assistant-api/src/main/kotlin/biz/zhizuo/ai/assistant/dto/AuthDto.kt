package biz.zhizuo.ai.assistant.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * 登录请求 DTO
 */
data class LoginRequest(
    @field:NotBlank(message = "用户名不能为空")
    val username: String,
    
    @field:NotBlank(message = "密码不能为空")
    val password: String,
    
    val rememberMe: Boolean = false
)

/**
 * 注册请求 DTO
 */
data class RegisterRequest(
    @field:NotBlank(message = "用户名不能为空")
    @field:Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    val username: String,
    
    @field:NotBlank(message = "邮箱不能为空")
    @field:Email(message = "邮箱格式不正确")
    val email: String,
    
    @field:NotBlank(message = "密码不能为空")
    @field:Size(min = 6, message = "密码长度至少6个字符")
    val password: String
)

/**
 * 用户信息 DTO
 */
data class UserDto(
    val id: String,
    val username: String,
    val email: String,
    val createdAt: LocalDateTime?
)

/**
 * 认证响应 DTO
 */
data class AuthResponse(
    val token: String,
    val user: UserDto,
    val expiresIn: Long
)

/**
 * 修改密码请求 DTO
 */
data class ChangePasswordRequest(
    @field:NotBlank(message = "当前密码不能为空")
    val currentPassword: String,
    
    @field:NotBlank(message = "新密码不能为空")
    @field:Size(min = 6, message = "新密码长度至少6个字符")
    val newPassword: String
)

/**
 * 重置密码请求 DTO
 */
data class ResetPasswordRequest(
    @field:NotBlank(message = "邮箱不能为空")
    @field:Email(message = "邮箱格式不正确")
    val email: String
)

/**
 * 更新密码请求 DTO
 */
data class UpdatePasswordRequest(
    @field:NotBlank(message = "重置令牌不能为空")
    val token: String,
    
    @field:NotBlank(message = "新密码不能为空")
    @field:Size(min = 6, message = "新密码长度至少6个字符")
    val newPassword: String
)

/**
 * 双因子认证状态响应 DTO
 */
data class TwoFactorStatusResponse(
    val enabled: Boolean,
    val qrCode: String? = null
)

/**
 * 启用双因子认证请求 DTO
 */
data class EnableTwoFactorRequest(
    @field:NotBlank(message = "验证码不能为空")
    val code: String
)

/**
 * 禁用双因子认证请求 DTO
 */
data class DisableTwoFactorRequest(
    @field:NotBlank(message = "验证码不能为空")
    val code: String
)
