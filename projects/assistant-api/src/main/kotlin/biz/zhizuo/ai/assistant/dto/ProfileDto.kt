package biz.zhizuo.ai.assistant.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDate

/**
 * 用户资料 DTO
 */
data class ProfileDto(
    val id: String?,
    val userId: String,
    val name: String,
    val avatar: String?,
    val email: String,
    val joinDate: LocalDate?,
    val bio: String?,
    val stats: ProfileStatsDto
)

/**
 * 用户资料统计 DTO
 */
data class ProfileStatsDto(
    val posts: Int,
    val followers: Int,
    val following: Int
)

/**
 * 更新用户资料请求 DTO
 */
data class UpdateProfileRequest(
    @field:NotBlank(message = "姓名不能为空")
    @field:Size(max = 100, message = "姓名长度不能超过100个字符")
    val name: String,
    
    @field:Size(max = 255, message = "头像URL长度不能超过255个字符")
    val avatar: String?,
    
    @field:NotBlank(message = "邮箱不能为空")
    @field:Email(message = "邮箱格式不正确")
    val email: String,
    
    @field:Size(max = 1000, message = "个人简介长度不能超过1000个字符")
    val bio: String?
)

/**
 * 用户资料响应 DTO
 */
data class ProfileResponse(
    val profile: ProfileDto,
    val activities: List<ActivityDto>
)

/**
 * 活动 DTO
 */
data class ActivityDto(
    val id: String,
    val type: String,
    val title: String,
    val description: String?,
    val timestamp: String,
    val icon: String?,
    val color: String?
)
