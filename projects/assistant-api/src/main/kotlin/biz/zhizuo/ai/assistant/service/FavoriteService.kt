package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.Favorite
import biz.zhizuo.ai.assistant.repository.FavoriteRepository
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 收藏服务
 */
@Service
@Transactional
class FavoriteService(
    private val favoriteRepository: FavoriteRepository
) {

    /**
     * 获取收藏数据
     */
    @Transactional(readOnly = true)
    fun getFavorites(): FavoriteResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal

        val allFavorites = favoriteRepository.findByUserIdOrderByCreatedAtDesc(userPrincipal.id)

        val articles = allFavorites.filter { it.type == "article" }.map { favorite ->
            FavoriteDto(
                id = favorite.id,
                type = favorite.type,
                title = favorite.title,
                description = favorite.description,
                url = favorite.url,
                image = favorite.image,
                author = favorite.author,
                publishedDate = favorite.publishedDate,
                readTime = favorite.readTime,
                duration = favorite.duration,
                viewCount = favorite.viewCount,
                createdAt = favorite.createdAt
            )
        }

        val videos = allFavorites.filter { it.type == "video" }.map { favorite ->
            FavoriteDto(
                id = favorite.id,
                type = favorite.type,
                title = favorite.title,
                description = favorite.description,
                url = favorite.url,
                image = favorite.image,
                author = favorite.author,
                publishedDate = favorite.publishedDate,
                readTime = favorite.readTime,
                duration = favorite.duration,
                viewCount = favorite.viewCount,
                createdAt = favorite.createdAt
            )
        }

        return FavoriteResponse(
            articles = articles,
            videos = videos
        )
    }

    /**
     * 添加收藏
     */
    fun addFavorite(request: AddFavoriteRequest): FavoriteDto {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal

        val favorite = Favorite(
            userId = userPrincipal.id,
            type = request.type,
            title = request.title,
            description = request.description,
            url = request.url,
            image = request.image,
            author = request.author,
            publishedDate = request.publishedDate,
            readTime = request.readTime,
            duration = request.duration,
            viewCount = request.viewCount
        )

        val savedFavorite = favoriteRepository.save(favorite)

        return FavoriteDto(
            id = savedFavorite.id,
            type = savedFavorite.type,
            title = savedFavorite.title,
            description = savedFavorite.description,
            url = savedFavorite.url,
            image = savedFavorite.image,
            author = savedFavorite.author,
            publishedDate = savedFavorite.publishedDate,
            readTime = savedFavorite.readTime,
            duration = savedFavorite.duration,
            viewCount = savedFavorite.viewCount,
            createdAt = savedFavorite.createdAt
        )
    }

    /**
     * 删除收藏
     */
    fun deleteFavorite(id: String): DeleteFavoriteResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal

        val favorite = favoriteRepository.findById(id)
            .orElseThrow { IllegalArgumentException("收藏不存在") }

        // 检查是否是当前用户的收藏
        if (favorite.userId != userPrincipal.id) {
            throw IllegalArgumentException("无权删除此收藏")
        }

        favoriteRepository.delete(favorite)

        return DeleteFavoriteResponse(
            success = true,
            message = "收藏删除成功"
        )
    }
}
