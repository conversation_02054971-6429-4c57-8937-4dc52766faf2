package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 关于信息实体
 */
@Entity
@Table(name = "about_info")
@EntityListeners(AuditingEntityListener::class)
data class AboutInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 50)
    val type: String, // 'company', 'team', 'milestone'

    @Column(nullable = false, length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @Column(length = 500)
    val image: String? = null,

    @Column(length = 100)
    val position: String? = null, // 用于团队成员的职位

    @Column(name = "date_value")
    val dateValue: String? = null, // 用于里程碑的日期

    @Column(name = "sort_order")
    val sortOrder: Int = 0,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime? = null,
)
