package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.SettingsResponse
import biz.zhizuo.ai.assistant.dto.UpdateSettingsRequest
import biz.zhizuo.ai.assistant.service.SettingsService
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 设置控制器
 */
@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = ["*"])
class SettingsController(
    private val settingsService: SettingsService
) {

    /**
     * 获取用户设置
     */
    @GetMapping
    fun getSettings(): ResponseEntity<SettingsResponse> {
        return try {
            val response = settingsService.getSettings()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }

    /**
     * 更新用户设置
     */
    @PatchMapping
    fun updateSettings(@Valid @RequestBody request: UpdateSettingsRequest): ResponseEntity<SettingsResponse> {
        return try {
            val response = settingsService.updateSettings(request)
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(400).body(null)
        }
    }
}
