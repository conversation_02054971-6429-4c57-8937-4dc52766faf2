package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.HelpInfoDto
import biz.zhizuo.ai.assistant.dto.HelpResponse
import biz.zhizuo.ai.assistant.repository.HelpInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 帮助信息服务
 */
@Service
@Transactional(readOnly = true)
class HelpService(
    private val helpInfoRepository: HelpInfoRepository
) {

    /**
     * 获取帮助信息
     */
    fun getHelp(): HelpResponse {
        val allInfo = helpInfoRepository.findAllByOrderBySortOrder()

        val faqs = allInfo.filter { it.type == "faq" }.map { info ->
            HelpInfoDto(
                id = info.id,
                type = info.type,
                title = info.title,
                content = info.content,
                category = info.category,
                icon = info.icon
            )
        }

        val categories = allInfo.filter { it.type == "category" }.map { info ->
            HelpInfoDto(
                id = info.id,
                type = info.type,
                title = info.title,
                content = info.content,
                category = info.category,
                icon = info.icon
            )
        }

        return HelpResponse(
            faqs = faqs,
            categories = categories
        )
    }
}
