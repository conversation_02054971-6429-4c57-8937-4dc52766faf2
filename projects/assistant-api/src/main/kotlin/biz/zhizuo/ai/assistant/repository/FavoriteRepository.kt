package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.Favorite
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 收藏数据访问接口
 */
@Repository
interface FavoriteRepository : JpaRepository<Favorite, String> {
    
    /**
     * 根据用户ID查找收藏
     */
    fun findByUserIdOrderByCreatedAtDesc(userId: String): List<Favorite>
    
    /**
     * 根据类型查找收藏
     */
    fun findByTypeOrderByCreatedAtDesc(type: String): List<Favorite>
    
    /**
     * 根据用户ID和类型查找收藏
     */
    fun findByUserIdAndTypeOrderByCreatedAtDesc(userId: String, type: String): List<Favorite>
}
