package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.MessageDto
import biz.zhizuo.ai.assistant.dto.MessageResponse
import biz.zhizuo.ai.assistant.repository.MessageRepository
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 消息服务
 */
@Service
@Transactional(readOnly = true)
class MessageService(
    private val messageRepository: MessageRepository
) {

    /**
     * 获取消息数据
     */
    fun getMessages(): MessageResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal

        // 获取通知消息
        val notifications = messageRepository.findByTypeOrderByCreatedAtDesc("notification").map { message ->
            MessageDto(
                id = message.id,
                type = message.type,
                category = message.category,
                title = message.title,
                content = message.content,
                isRead = message.isRead,
                icon = message.icon,
                color = message.color,
                createdAt = message.createdAt
            )
        }

        // 获取私信消息
        val privateMessages = messageRepository.findByTypeAndUserIdOrderByCreatedAtDesc("private", userPrincipal.id).map { message ->
            MessageDto(
                id = message.id,
                type = message.type,
                category = message.category,
                title = message.title,
                content = message.content,
                isRead = message.isRead,
                icon = message.icon,
                color = message.color,
                createdAt = message.createdAt
            )
        }

        return MessageResponse(
            notifications = notifications,
            privateMessages = privateMessages
        )
    }
}
