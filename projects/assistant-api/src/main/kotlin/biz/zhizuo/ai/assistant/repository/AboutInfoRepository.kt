package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.AboutInfo
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 关于信息数据访问接口
 */
@Repository
interface AboutInfoRepository : JpaRepository<AboutInfo, String> {
    
    /**
     * 根据类型查找信息
     */
    fun findByTypeOrderBySortOrder(type: String): List<AboutInfo>
    
    /**
     * 查找所有信息并按排序顺序排列
     */
    fun findAllByOrderBySortOrder(): List<AboutInfo>
}
