package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.HelpResponse
import biz.zhizuo.ai.assistant.service.HelpService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 帮助信息控制器
 */
@RestController
@RequestMapping("/api/help")
@CrossOrigin(origins = ["*"])
class HelpController(
    private val helpService: HelpService
) {

    /**
     * 获取帮助信息
     */
    @GetMapping
    fun getHelp(): ResponseEntity<HelpResponse> {
        return try {
            val response = helpService.getHelp()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }
}
