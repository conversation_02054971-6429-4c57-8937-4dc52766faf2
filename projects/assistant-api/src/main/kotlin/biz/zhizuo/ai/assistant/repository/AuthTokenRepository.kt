package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.AuthToken
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.*

/**
 * 认证令牌数据访问接口
 */
@Repository
interface AuthTokenRepository : JpaRepository<AuthToken, String> {
    
    /**
     * 根据令牌查找
     */
    fun findByToken(token: String): Optional<AuthToken>
    
    /**
     * 根据用户ID查找所有令牌
     */
    fun findByUserId(userId: String): List<AuthToken>
    
    /**
     * 删除用户的所有令牌
     */
    fun deleteByUserId(userId: String)
    
    /**
     * 删除过期的令牌
     */
    @Modifying
    @Query("DELETE FROM AuthToken t WHERE t.expiresAt < :now")
    fun deleteExpiredTokens(now: LocalDateTime)
    
    /**
     * 根据令牌和过期时间查找有效令牌
     */
    @Query("SELECT t FROM AuthToken t WHERE t.token = :token AND t.expiresAt > :now")
    fun findValidToken(token: String, now: LocalDateTime): Optional<AuthToken>
}
