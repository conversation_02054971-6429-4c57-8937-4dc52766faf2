package biz.zhizuo.ai.assistant.security

import io.jsonwebtoken.*
import io.jsonwebtoken.security.Keys
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import java.security.Key
import java.util.*

/**
 * JWT 令牌提供者
 */
@Component
class JwtTokenProvider {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Value("\${app.jwt.secret:mySecretKey}")
    private lateinit var jwtSecret: String

    @Value("\${app.jwt.expiration:86400000}") // 24小时
    private var jwtExpirationInMs: Long = 86400000

    @Value("\${app.jwt.remember-me-expiration:**********}") // 30天
    private var jwtRememberMeExpirationInMs: Long = **********

    private val key: Key by lazy {
        Keys.hmacShaKeyFor(jwtSecret.toByteArray())
    }

    /**
     * 生成 JWT 令牌
     */
    fun generateToken(authentication: Authentication, rememberMe: Boolean = false): String {
        val userPrincipal = authentication.principal as UserPrincipal
        val expiryDate =
            Date(System.currentTimeMillis() + if (rememberMe) jwtRememberMeExpirationInMs else jwtExpirationInMs)

        return Jwts.builder()
            .subject(userPrincipal.id.toString())
            .claim("username", userPrincipal.username)
            .claim("email", userPrincipal.email)
            .issuedAt(Date())
            .expiration(expiryDate)
            .signWith(key, SignatureAlgorithm.HS512)
            .compact()
    }

    /**
     * 从令牌中获取用户ID
     */
    fun getUserIdFromToken(token: String): String {
        val claims = Jwts.parser()
            .verifyWith(key as javax.crypto.SecretKey)
            .build()
            .parseSignedClaims(token)
            .payload

        return claims.subject
    }

    /**
     * 从令牌中获取用户名
     */
    fun getUsernameFromToken(token: String): String {
        val claims = Jwts.parser()
            .verifyWith(key as javax.crypto.SecretKey)
            .build()
            .parseSignedClaims(token)
            .payload

        return claims["username"] as String
    }

    /**
     * 验证令牌
     */
    fun validateToken(authToken: String): Boolean {
        try {
            Jwts.parser()
                .verifyWith(key as javax.crypto.SecretKey)
                .build()
                .parseSignedClaims(authToken)
            return true
        } catch (ex: SecurityException) {
            logger.info("Invalid JWT signature")
        } catch (ex: MalformedJwtException) {
            logger.info("Invalid JWT token")
        } catch (ex: ExpiredJwtException) {
            logger.info("Expired JWT token")
        } catch (ex: UnsupportedJwtException) {
            logger.info("Unsupported JWT token")
        } catch (ex: IllegalArgumentException) {
            logger.info("JWT claims string is empty")
        }
        return false
    }

    /**
     * 获取令牌过期时间
     */
    fun getExpirationDateFromToken(token: String): Date {
        val claims = Jwts.parser()
            .verifyWith(key as javax.crypto.SecretKey)
            .build()
            .parseSignedClaims(token)
            .payload

        return claims.expiration
    }

    /**
     * 检查令牌是否过期
     */
    fun isTokenExpired(token: String): Boolean {
        val expiration = getExpirationDateFromToken(token)
        return expiration.before(Date())
    }
}
