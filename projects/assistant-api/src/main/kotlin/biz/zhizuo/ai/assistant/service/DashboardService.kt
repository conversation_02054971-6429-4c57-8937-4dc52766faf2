package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.DashboardActivityDto
import biz.zhizuo.ai.assistant.dto.DashboardResponse
import biz.zhizuo.ai.assistant.dto.DashboardStatDto
import biz.zhizuo.ai.assistant.repository.DashboardDataRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 仪表盘服务
 */
@Service
@Transactional(readOnly = true)
class DashboardService(
    private val dashboardDataRepository: DashboardDataRepository
) {

    /**
     * 获取仪表盘数据
     */
    fun getDashboard(): DashboardResponse {
        val allData = dashboardDataRepository.findAllByOrderBySortOrder()

        val stats = allData.filter { it.type == "stat" }.map { data ->
            DashboardStatDto(
                title = data.title,
                value = data.value,
                icon = data.icon,
                color = data.color
            )
        }

        val activities = allData.filter { it.type == "activity" }.map { data ->
            DashboardActivityDto(
                title = data.title,
                description = data.description,
                value = data.value,
                icon = data.icon,
                color = data.color
            )
        }

        return DashboardResponse(
            stats = stats,
            activities = activities
        )
    }
}
