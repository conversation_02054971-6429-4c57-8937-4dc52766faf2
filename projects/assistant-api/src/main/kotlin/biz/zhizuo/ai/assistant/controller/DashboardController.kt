package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.DashboardResponse
import biz.zhizuo.ai.assistant.service.DashboardService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 仪表盘控制器
 */
@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = ["*"])
class DashboardController(
    private val dashboardService: DashboardService
) {

    /**
     * 获取仪表盘数据
     */
    @GetMapping
    fun getDashboard(): ResponseEntity<DashboardResponse> {
        return try {
            val response = dashboardService.getDashboard()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }
}
