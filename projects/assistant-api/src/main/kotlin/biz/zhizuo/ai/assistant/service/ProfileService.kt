package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.repository.UserProfileRepository
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 用户资料服务
 */
@Service
@Transactional
class ProfileService(
    private val userProfileRepository: UserProfileRepository
) {

    /**
     * 获取用户资料
     */
    fun getProfile(): ProfileResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val profile = userProfileRepository.findByUserId(userPrincipal.id)
            .orElseThrow { IllegalArgumentException("用户资料不存在") }

        val profileDto = ProfileDto(
            id = profile.id,
            userId = profile.userId,
            name = profile.name,
            avatar = profile.avatar,
            email = profile.email,
            joinDate = profile.joinDate,
            bio = profile.bio,
            stats = ProfileStatsDto(
                posts = profile.postsCount,
                followers = profile.followersCount,
                following = profile.followingCount
            )
        )

        // 模拟活动数据
        val activities = listOf(
            ActivityDto(
                id = "1",
                type = "post",
                title = "发布了新文章",
                description = "《如何使用 Spring Boot 构建 REST API》",
                timestamp = "2024-01-15T10:30:00Z",
                icon = "edit",
                color = "primary"
            ),
            ActivityDto(
                id = "2",
                type = "comment",
                title = "评论了文章",
                description = "对《微服务架构设计》发表了评论",
                timestamp = "2024-01-14T15:20:00Z",
                icon = "comment",
                color = "secondary"
            ),
            ActivityDto(
                id = "3",
                type = "like",
                title = "点赞了文章",
                description = "《前端开发最佳实践》",
                timestamp = "2024-01-13T09:15:00Z",
                icon = "favorite",
                color = "accent"
            )
        )

        return ProfileResponse(
            profile = profileDto,
            activities = activities
        )
    }

    /**
     * 更新用户资料
     */
    fun updateProfile(request: UpdateProfileRequest): ProfileDto {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val profile = userProfileRepository.findByUserId(userPrincipal.id)
            .orElseThrow { IllegalArgumentException("用户资料不存在") }

        val updatedProfile = profile.copy(
            name = request.name,
            avatar = request.avatar,
            email = request.email,
            bio = request.bio
        )

        val savedProfile = userProfileRepository.save(updatedProfile)

        return ProfileDto(
            id = savedProfile.id,
            userId = savedProfile.userId,
            name = savedProfile.name,
            avatar = savedProfile.avatar,
            email = savedProfile.email,
            joinDate = savedProfile.joinDate,
            bio = savedProfile.bio,
            stats = ProfileStatsDto(
                posts = savedProfile.postsCount,
                followers = savedProfile.followersCount,
                following = savedProfile.followingCount
            )
        )
    }
}
