package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.Message
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 消息数据访问接口
 */
@Repository
interface MessageRepository : JpaRepository<Message, String> {
    
    /**
     * 根据类型查找消息
     */
    fun findByTypeOrderByCreatedAtDesc(type: String): List<Message>
    
    /**
     * 根据用户ID查找消息
     */
    fun findByUserIdOrderByCreatedAtDesc(userId: String): List<Message>
    
    /**
     * 根据类型和用户ID查找消息
     */
    fun findByTypeAndUserIdOrderByCreatedAtDesc(type: String, userId: String): List<Message>
    
    /**
     * 查找所有消息并按创建时间倒序排列
     */
    fun findAllByOrderByCreatedAtDesc(): List<Message>
}
