package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.ProfileResponse
import biz.zhizuo.ai.assistant.dto.UpdateProfileRequest
import biz.zhizuo.ai.assistant.service.ProfileService
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 用户资料控制器
 */
@RestController
@RequestMapping("/api/profile")
@CrossOrigin(origins = ["*"])
class ProfileController(
    private val profileService: ProfileService
) {

    /**
     * 获取用户资料
     */
    @GetMapping
    fun getProfile(): ResponseEntity<ProfileResponse> {
        return try {
            val response = profileService.getProfile()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }

    /**
     * 更新用户资料
     */
    @PatchMapping
    fun updateProfile(@Valid @RequestBody request: UpdateProfileRequest): ResponseEntity<Map<String, Any>> {
        return try {
            val profile = profileService.updateProfile(request)
            ResponseEntity.ok(mapOf(
                "message" to "资料更新成功",
                "profile" to profile
            ))
        } catch (e: Exception) {
            ResponseEntity.status(400).body(mapOf(
                "message" to (e.message ?: "资料更新失败")
            ))
        }
    }
}
