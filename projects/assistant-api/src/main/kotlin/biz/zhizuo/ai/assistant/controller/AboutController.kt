package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.AboutResponse
import biz.zhizuo.ai.assistant.service.AboutService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 关于信息控制器
 */
@RestController
@RequestMapping("/api/about")
@CrossOrigin(origins = ["*"])
class AboutController(
    private val aboutService: AboutService
) {

    /**
     * 获取关于信息
     */
    @GetMapping
    fun getAbout(): ResponseEntity<AboutResponse> {
        return try {
            val response = aboutService.getAbout()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }
}
