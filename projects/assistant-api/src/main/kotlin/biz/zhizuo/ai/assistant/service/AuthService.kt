package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.User
import biz.zhizuo.ai.assistant.entity.UserProfile
import biz.zhizuo.ai.assistant.entity.UserSettings
import biz.zhizuo.ai.assistant.repository.UserProfileRepository
import biz.zhizuo.ai.assistant.repository.UserRepository
import biz.zhizuo.ai.assistant.repository.UserSettingsRepository
import biz.zhizuo.ai.assistant.security.JwtTokenProvider
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

/**
 * 认证服务
 */
@Service
@Transactional
class AuthService(
    private val authenticationManager: AuthenticationManager,
    private val userRepository: UserRepository,
    private val userProfileRepository: UserProfileRepository,
    private val userSettingsRepository: UserSettingsRepository,
    private val passwordEncoder: PasswordEncoder,
    private val tokenProvider: JwtTokenProvider
) {

    /**
     * 用户登录
     */
    fun login(request: LoginRequest): AuthResponse {
        val authentication = authenticationManager.authenticate(
            UsernamePasswordAuthenticationToken(
                request.username,
                request.password
            )
        )

        SecurityContextHolder.getContext().authentication = authentication

        val jwt = tokenProvider.generateToken(authentication, request.rememberMe)
        val userPrincipal = authentication.principal as UserPrincipal

        return AuthResponse(
            token = jwt,
            user = UserDto(
                id = userPrincipal.id,
                username = userPrincipal.username,
                email = userPrincipal.email,
                createdAt = null // 可以从数据库获取
            ),
            expiresIn = if (request.rememberMe) 2592000000L else 86400000L
        )
    }

    /**
     * 用户注册
     */
    fun register(request: RegisterRequest): AuthResponse {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.username)) {
            throw IllegalArgumentException("用户名已存在")
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.email)) {
            throw IllegalArgumentException("邮箱已存在")
        }

        // 创建用户
        val user = userRepository.save(
            User(
                username = request.username,
                email = request.email,
                password = passwordEncoder.encode(request.password)
            )
        )

        // 创建默认用户资料
        userProfileRepository.save(
            UserProfile(
                userId = user.id!!,
                name = user.username,
                avatar = "assets/default-avatar.svg",
                email = user.email,
                joinDate = LocalDate.now(),
                bio = ""
            )
        )

        // 创建默认用户设置
        userSettingsRepository.save(
            UserSettings(
                userId = user.id!!
            )
        )

        // 自动登录
        val authentication = UsernamePasswordAuthenticationToken(
            UserPrincipal.create(user),
            null,
            UserPrincipal.create(user).authorities
        )
        SecurityContextHolder.getContext().authentication = authentication

        val jwt = tokenProvider.generateToken(authentication, false)

        return AuthResponse(
            token = jwt,
            user = UserDto(
                id = user.id!!,
                username = user.username,
                email = user.email,
                createdAt = user.createdAt
            ),
            expiresIn = 86400000L
        )
    }

    /**
     * 获取当前用户信息
     */
    fun getCurrentUser(): UserDto {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val user = userRepository.findById(userPrincipal.id)
            .orElseThrow { IllegalArgumentException("用户不存在") }

        return UserDto(
            id = user.id!!,
            username = user.username,
            email = user.email,
            createdAt = user.createdAt
        )
    }

    /**
     * 修改密码
     */
    fun changePassword(request: ChangePasswordRequest) {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val user = userRepository.findById(userPrincipal.id)
            .orElseThrow { IllegalArgumentException("用户不存在") }

        // 验证当前密码
        if (!passwordEncoder.matches(request.currentPassword, user.password)) {
            throw IllegalArgumentException("当前密码错误")
        }

        // 更新密码
        val updatedUser = user.copy(password = passwordEncoder.encode(request.newPassword))
        userRepository.save(updatedUser)
    }

    /**
     * 重置密码（发送重置邮件）
     */
    fun resetPassword(request: ResetPasswordRequest) {
        val user = userRepository.findByEmail(request.email)
            .orElseThrow { IllegalArgumentException("邮箱不存在") }

        // TODO: 实现发送重置密码邮件的逻辑
        // 这里应该生成重置令牌并发送邮件
        throw UnsupportedOperationException("重置密码功能尚未实现")
    }

    /**
     * 更新密码（通过重置令牌）
     */
    fun updatePassword(request: UpdatePasswordRequest) {
        // TODO: 实现通过重置令牌更新密码的逻辑
        throw UnsupportedOperationException("更新密码功能尚未实现")
    }

    /**
     * 获取双因子认证状态
     */
    fun getTwoFactorStatus(): TwoFactorStatusResponse {
        val userPrincipal = SecurityContextHolder.getContext().authentication.principal as UserPrincipal
        val user = userRepository.findById(userPrincipal.id)
            .orElseThrow { IllegalArgumentException("用户不存在") }

        return TwoFactorStatusResponse(
            enabled = user.twoFactorEnabled,
            qrCode = if (!user.twoFactorEnabled) "data:image/png;base64,..." else null
        )
    }

    /**
     * 启用双因子认证
     */
    fun enableTwoFactor(request: EnableTwoFactorRequest) {
        // TODO: 实现双因子认证启用逻辑
        throw UnsupportedOperationException("双因子认证功能尚未实现")
    }

    /**
     * 禁用双因子认证
     */
    fun disableTwoFactor(request: DisableTwoFactorRequest) {
        // TODO: 实现双因子认证禁用逻辑
        throw UnsupportedOperationException("双因子认证功能尚未实现")
    }
}
