package biz.zhizuo.ai.assistant.controller

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 联系方式控制器
 */
@RestController
@RequestMapping("/api/contact")
@CrossOrigin(origins = ["*"])
class ContactController {

    /**
     * 获取联系方式信息
     */
    @GetMapping
    fun getContact(): ResponseEntity<Map<String, Any>> {
        val contactInfo = mapOf(
            "company" to mapOf(
                "name" to "智作科技",
                "address" to "北京市朝阳区xxx街道xxx号",
                "phone" to "+86 ************",
                "email" to "<EMAIL>",
                "website" to "https://www.zhizuo.biz"
            ),
            "support" to mapOf(
                "email" to "<EMAIL>",
                "phone" to "+86 ************",
                "hours" to "周一至周五 9:00-18:00"
            ),
            "social" to mapOf(
                "wechat" to "zhizuo_tech",
                "weibo" to "@智作科技",
                "github" to "https://github.com/zhizuo"
            )
        )

        return ResponseEntity.ok(contactInfo)
    }

    /**
     * 发送联系消息
     */
    @PostMapping
    fun sendMessage(@RequestBody request: Map<String, Any>): ResponseEntity<Map<String, Any>> {
        // TODO: 实现发送联系消息的逻辑
        return ResponseEntity.ok(mapOf(
            "success" to true,
            "message" to "消息发送成功，我们会尽快回复您"
        ))
    }
}
