package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.AboutInfoDto
import biz.zhizuo.ai.assistant.dto.AboutResponse
import biz.zhizuo.ai.assistant.repository.AboutInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 关于信息服务
 */
@Service
@Transactional(readOnly = true)
class AboutService(
    private val aboutInfoRepository: AboutInfoRepository
) {

    /**
     * 获取关于信息
     */
    fun getAbout(): AboutResponse {
        val allInfo = aboutInfoRepository.findAllByOrderBySortOrder()

        val company = allInfo.filter { it.type == "company" }.map { info ->
            AboutInfoDto(
                id = info.id,
                type = info.type,
                title = info.title,
                description = info.description,
                content = info.content,
                image = info.image,
                position = info.position,
                dateValue = info.dateValue
            )
        }

        val team = allInfo.filter { it.type == "team" }.map { info ->
            AboutInfoDto(
                id = info.id,
                type = info.type,
                title = info.title,
                description = info.description,
                content = info.content,
                image = info.image,
                position = info.position,
                dateValue = info.dateValue
            )
        }

        val milestones = allInfo.filter { it.type == "milestone" }.map { info ->
            AboutInfoDto(
                id = info.id,
                type = info.type,
                title = info.title,
                description = info.description,
                content = info.content,
                image = info.image,
                position = info.position,
                dateValue = info.dateValue
            )
        }

        return AboutResponse(
            company = company,
            team = team,
            milestones = milestones
        )
    }
}
