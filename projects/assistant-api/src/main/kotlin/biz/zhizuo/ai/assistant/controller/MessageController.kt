package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.MessageResponse
import biz.zhizuo.ai.assistant.service.MessageService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 消息控制器
 */
@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = ["*"])
class MessageController(
    private val messageService: MessageService
) {

    /**
     * 获取消息数据
     */
    @GetMapping
    fun getMessages(): ResponseEntity<MessageResponse> {
        return try {
            val response = messageService.getMessages()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }
}
