import { TestBed } from '@angular/core/testing';
import { SpeechRecognitionService } from './speech-recognition.service';

describe('SpeechRecognitionService', () => {
  let service: SpeechRecognitionService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SpeechRecognitionService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should check browser support', () => {
    // 这个测试只是检查方法是否存在，实际支持情况取决于浏览器
    expect(typeof service.isSupported).toBe('function');
  });

  it('should have start and stop methods', () => {
    expect(typeof service.start).toBe('function');
    expect(typeof service.stop).toBe('function');
  });

  it('should expose isListening$ observable', () => {
    expect(service.isListening$).toBeDefined();
  });

  // 测试触发词检测功能
  it('should detect trigger words and remove them from text', () => {
    // 使用私有方法进行测试
    const checkForTriggerWords = (service as any).checkForTriggerWords.bind(service);

    // 测试各种触发词
    expect(checkForTriggerWords('这是一段测试文本说完了').text).toBe('这是一段测试文本');
    expect(checkForTriggerWords('这是一段测试文本说完了').triggered).toBe(true);

    expect(checkForTriggerWords('请帮我分析这个问题回答吧').text).toBe('请帮我分析这个问题');
    expect(checkForTriggerWords('请帮我分析这个问题回答吧').triggered).toBe(true);

    expect(checkForTriggerWords('这是没有触发词的文本').text).toBe('这是没有触发词的文本');
    expect(checkForTriggerWords('这是没有触发词的文本').triggered).toBe(false);

    // 测试带标点符号的触发词
    expect(checkForTriggerWords('这是一段测试文本说完了。').text).toBe('这是一段测试文本');
    expect(checkForTriggerWords('这是一段测试文本说完了。').triggered).toBe(true);

    expect(checkForTriggerWords('请帮我分析这个问题回答吧！').text).toBe('请帮我分析这个问题');
    expect(checkForTriggerWords('请帮我分析这个问题回答吧！').triggered).toBe(true);

    // 测试多个标点符号
    expect(checkForTriggerWords('这是一段测试文本说完了！！！').text).toBe('这是一段测试文本');
    expect(checkForTriggerWords('这是一段测试文本说完了！！！').triggered).toBe(true);

    // 测试末尾空白字符
    expect(checkForTriggerWords('这是一段测试文本说完了  ').text).toBe('这是一段测试文本');
    expect(checkForTriggerWords('这是一段测试文本说完了  ').triggered).toBe(true);

    // 测试标点符号和空白字符的组合
    expect(checkForTriggerWords('这是一段测试文本说完了！  ').text).toBe('这是一段测试文本');
    expect(checkForTriggerWords('这是一段测试文本说完了！  ').triggered).toBe(true);
  });

  // 测试触发词检测方法
  it('should detect trigger words correctly', () => {
    // 直接测试私有方法
    const checkForTriggerWords = (service as any).checkForTriggerWords.bind(service);

    // 测试各种触发词场景
    const result1 = checkForTriggerWords('这是测试文本说完了');
    expect(result1.text).toBe('这是测试文本');
    expect(result1.triggered).toBe(true);

    const result2 = checkForTriggerWords('请帮我分析这个问题回答吧');
    expect(result2.text).toBe('请帮我分析这个问题');
    expect(result2.triggered).toBe(true);

    const result3 = checkForTriggerWords('这是没有触发词的文本');
    expect(result3.text).toBe('这是没有触发词的文本');
    expect(result3.triggered).toBe(false);

    // 测试带标点符号的触发词
    const result4 = checkForTriggerWords('这是测试文本说完了。');
    expect(result4.text).toBe('这是测试文本');
    expect(result4.triggered).toBe(true);

    const result5 = checkForTriggerWords('请帮我分析这个问题回答吧！！');
    expect(result5.text).toBe('请帮我分析这个问题');
    expect(result5.triggered).toBe(true);

    // 测试末尾空白字符
    const result6 = checkForTriggerWords('这是测试文本说完了   ');
    expect(result6.text).toBe('这是测试文本');
    expect(result6.triggered).toBe(true);

    // 测试标点符号和空白字符的组合
    const result7 = checkForTriggerWords('这是测试文本说完了！  ');
    expect(result7.text).toBe('这是测试文本');
    expect(result7.triggered).toBe(true);
  });

  // 测试触发词回调功能 - 使用更简单的方法
  it('should handle trigger callbacks correctly', () => {
    // 模拟私有方法和回调
    const callback = jest.fn();
    const triggerCallback = jest.fn();

    // 设置服务内部状态
    (service as any)._isSupported = true;
    (service as any).triggerCallback = triggerCallback;

    // 直接调用私有方法模拟语音识别结果
    const processedText = (service as any).checkForTriggerWords('这是测试文本说完了');

    // 模拟调用回调
    callback(processedText.text, true);
    if (processedText.triggered) {
      triggerCallback(processedText.text);
    }

    // 验证回调被调用，并且触发词被移除
    expect(callback).toHaveBeenCalledWith('这是测试文本', true);
    expect(triggerCallback).toHaveBeenCalledWith('这是测试文本');
  });
});
