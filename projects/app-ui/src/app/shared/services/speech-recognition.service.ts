import { Injectable, NgZone } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

// 为了处理浏览器兼容性，创建一个接口来表示SpeechRecognition
interface IWindow extends Window {
  webkitSpeechRecognition: any;
  SpeechRecognition: any;
}

// 定义命令列表
const COMMANDS = [
  '说完了',
  '回答吧',
  '请回答',
  '帮我回答',
  '就这样',
  '就这些',
  '完毕',
  '结束',
  '结束了',
  '发送',
  '提交',
];

@Injectable({
  providedIn: 'root',
})
export class SpeechRecognitionService {
  private recognition: any;
  private isStoppedSpeechRecog = false;
  private _isListening = new BehaviorSubject<boolean>(false);
  private _isSupported = false;
  private isStarting = false; // 标记语音识别是否正在启动中

  // 公开的可观察对象，用于监听状态变化
  public isListening$ = this._isListening.asObservable();

  // 触发词检测回调
  private triggerCallback: ((text: string) => void) | null = null;

  constructor(private zone: NgZone) {
    this.initRecognition();
  }

  /**
   * 初始化语音识别
   */
  private initRecognition(): void {
    try {
      const { webkitSpeechRecognition, SpeechRecognition }: IWindow =
        window as unknown as IWindow;
      // 检查浏览器支持
      const SpeechRecognitionAPI = SpeechRecognition || webkitSpeechRecognition;

      if (SpeechRecognitionAPI) {
        this._isSupported = true;
        this.recognition = new SpeechRecognitionAPI();
        this.recognition.continuous = true; // 设置为持续模式，持续进行语音识别
        this.recognition.interimResults = true;
        this.recognition.lang = 'zh-CN'; // 默认使用中文识别
      }
    } catch (error) {
      console.error('语音识别初始化失败:', error);
      this._isSupported = false;
    }
  }

  /**
   * 检查语音识别是否被支持
   */
  public isSupported(): boolean {
    return this._isSupported;
  }

  /**
   * 开始语音识别
   * @param callback 识别结果回调函数
   * @param triggerCallback 触发词检测到时的回调函数
   */
  public start(
    callback: (text: string, isFinal: boolean) => void,
    triggerCallback?: (text: string) => void
  ): void {
    if (!this._isSupported) {
      console.warn('当前浏览器不支持语音识别');
      return;
    }

    try {
      this.isStarting = true;
      this.isStoppedSpeechRecog = false;
      this._isListening.next(true);
      this.triggerCallback = triggerCallback || null;

      // 设置事件处理程序
      this.recognition.onresult = (event: any) => {
        let interimTranscript = '';
        let finalTranscript = '';

        // 确保在Angular区域内执行回调
        this.zone.run(() => {
          for (let i = event.resultIndex; i < event.results.length; ++i) {
            if (event.results[i].isFinal) {
              finalTranscript += event.results[i][0].transcript;
              console.log('语音识别结果:', finalTranscript);
              // 检查是否包含触发词
              const processedText = this.checkForcommands(finalTranscript);
              console.log('处理后的文本:', processedText.text);
              console.log('是否触发:', processedText.triggered);

              // 如果检测到触发词并且有回调函数，则调用回调
              if (processedText.triggered && this.triggerCallback) {
                console.log('触发回调函数:', processedText.text);
                this.triggerCallback(processedText.text);
              } else {
                callback(processedText.text, true);
              }
            } else {
              interimTranscript += event.results[i][0].transcript;
              callback(interimTranscript, false);
            }
          }
        });
      };

      this.recognition.onend = () => {
        this.zone.run(() => {
          console.log('语音识别结束事件触发');
          this.isStarting = false;

          // 如果不是用户主动停止，则自动重新启动
          if (this.isStoppedSpeechRecog) {
            // 用户主动停止，更新UI状态
            console.log('用户主动停止语音识别');
            this._isListening.next(false);
          } else {
            // 语音识别服务自动停止，尝试重新启动
            console.log('语音识别意外停止，尝试重新启动');

            // 等待一小段时间再重新启动，避免可能的冲突
            setTimeout(() => {
              try {
                // 保持UI状态不变，用户仍然处于语音识别模式
                if (!this.isStoppedSpeechRecog) {
                  console.log('重新启动语音识别');
                  this.recognition.start();
                }
              } catch (error) {
                console.error('重新启动语音识别失败:', error);
                // 只有在重新启动失败时才更新UI状态
                this._isListening.next(false);
              }
            }, 300);
          }
        });
      };

      this.recognition.onerror = (event: any) => {
        this.zone.run(() => {
          console.error('语音识别错误:', event.error);
          this.isStarting = false;
          // 只有在错误情况下才更新UI状态
          this._isListening.next(false);
        });
      };

      // 启动语音识别
      this.recognition.start();
    } catch (error) {
      console.error('启动语音识别失败:', error);
      this.isStarting = false;
      this._isListening.next(false);
    }
  }

  /**
   * 停止语音识别
   */
  public stop(): void {
    if (!this._isSupported) {
      return;
    }

    try {
      this.isStoppedSpeechRecog = true;
      this.recognition.stop();
      this._isListening.next(false);
      this.triggerCallback = null;
    } catch (error) {
      console.error('停止语音识别失败:', error);
      // 即使停止失败，也要确保状态正确
      this._isListening.next(false);
      this.isStarting = false;
    }
  }

  /**
   * 检查文本是否包含触发词
   * @param text 要检查的文本
   * @returns 是否触发的标志
   */
  private checkForcommands(text: string): {
    text: string;
    triggered: boolean;
    command?: string;
  } {
    // 如果文本为空，直接返回
    if (!text || text.trim() === '') {
      return { text, triggered: false };
    }

    // 将文本转为小写并移除末尾的标点符号和空白
    const trimmedText = text.replace(/(\p{P}|\s)+$/u, '');

    // 检查是否以触发词结尾
    for (const command of COMMANDS) {
      if (trimmedText.toLowerCase().endsWith(command.toLowerCase())) {
        // 找到触发词在原始文本中的位置
        const triggerIndex = text
          .toLowerCase()
          .lastIndexOf(command.toLowerCase());
        if (triggerIndex !== -1) {
          // 移除触发词，保留前面的内容
          const processedText = text.substring(0, triggerIndex).trim();
          return { text: processedText, command: command, triggered: true };
        }
      }
    }

    return { text, triggered: false };
  }
}
