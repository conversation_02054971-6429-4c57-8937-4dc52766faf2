/**
 * 初始化 Service Worker
 * 在 Angular 应用启动前注册和初始化 Service Worker
 */
export async function initServiceWorker(): Promise<void> {
  // 检查浏览器是否支持 Service Worker
  if (!('serviceWorker' in navigator)) {
    console.warn('当前浏览器不支持 Service Worker');
    return;
  }

  // 等待页面加载完成后再注册 Service Worker
  await waitForLoad();
  await registerServiceWorker();
}

/**
 * 等待页面加载完成
 * @returns 返回 Promise，当页面加载完成时解决
 */
async function waitForLoad(): Promise<void> {
  // 如果页面已经加载完成，直接返回
  if (document.readyState === 'complete') {
    return;
  }

  // 否则等待 load 事件
  return new Promise<void>((resolve) => {
    window.addEventListener('load', () => resolve());
  });
}

/**
 * 等待 Service Worker 激活
 * @param worker 正在安装的 Service Worker
 * @returns 返回 Promise，当 Service Worker 激活时解决
 */
async function waitForActivation(worker: ServiceWorker): Promise<void> {
  return new Promise<void>((resolveActivation) => {
    worker.addEventListener('statechange', () => {
      if (worker.state === 'activated') {
        resolveActivation();
      }
    });
  });
}

/**
 * 注册 Service Worker
 */
async function registerServiceWorker(): Promise<void> {
  try {
    // 首先检查并清除现有的 Service Worker 注册
    const registrations = await navigator.serviceWorker.getRegistrations();
    registrations.forEach((it) => it.unregister());

    // 注册新的 Service Worker
    const registration = await navigator.serviceWorker.register(
      '/service-worker.js',
      { scope: '/', updateViaCache: 'none', type: 'module' }
    );

    // 等待 Service Worker 激活
    if (registration.installing) {
      // 等待 Service Worker 安装完成
      await waitForActivation(registration.installing);
    } else if (registration.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }

    // 强制刷新所有客户端
    if (registration.active) {
      registration.active.postMessage({ type: 'CLAIM_CLIENTS' });
    }

    await navigator.serviceWorker.ready;

    // 检查 Service Worker 是否控制页面
    if (!navigator.serviceWorker.controller && registration.active) {
      // 添加消息监听器，处理来自 Service Worker 的消息
      navigator.serviceWorker.addEventListener('message', (event) => {
        // 处理消息
      });

      // 刷新页面以应用 Service Worker
      window.location.reload();
    }
  } catch (error) {
    console.error('Service Worker 注册失败:', error);
    // 即使注册失败，也允许应用继续启动
  }
}
