import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { MarkdownPipe } from '../pipes/markdown.pipe';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-markdown',
  templateUrl: './markdown.component.html',
  styleUrls: ['./markdown.component.scss'],
  standalone: true,
  imports: [MarkdownPipe, AsyncPipe],
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class MarkdownComponent {
  @Input() content = '';
}
