import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By, DomSanitizer } from '@angular/platform-browser';
import { MarkdownComponent } from './markdown.component';
import { MarkdownPipe } from '../pipes/markdown.pipe';
import { AsyncPipe } from '@angular/common';

describe('MarkdownComponent', () => {
  let component: MarkdownComponent;
  let fixture: ComponentFixture<MarkdownComponent>;
  let mockMarkdownPipe: any;
  let mockAsyncPipe: any;
  let mockSanitizer: any;

  beforeEach(async () => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock for DomSanitizer
    mockSanitizer = {
      bypassSecurityTrustHtml: jest.fn((value) => value),
    };

    // Setup mock for MarkdownPipe
    mockMarkdownPipe = {
      transform: jest
        .fn()
        .mockImplementation((value) => Promise.resolve(`<p>${value}</p>`)),
    };

    // Setup mock for AsyncPipe
    mockAsyncPipe = {
      transform: jest.fn().mockImplementation((value) => `<p>${value}</p>`),
    };

    await TestBed.configureTestingModule({
      imports: [MarkdownComponent],
      providers: [
        { provide: MarkdownPipe, useValue: mockMarkdownPipe },
        { provide: AsyncPipe, useValue: mockAsyncPipe },
        { provide: DomSanitizer, useValue: mockSanitizer },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MarkdownComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render content correctly', () => {
    // 清除之前的调用记录
    mockMarkdownPipe.transform.mockClear();
    mockAsyncPipe.transform.mockClear();

    // Setup
    const testContent = 'Test content';
    const transformedContent = '<p>Test content</p>';

    // Set input
    component.content = testContent;
    fixture.detectChanges();

    // 获取 HTML 内容
    const contentElement = fixture.debugElement.query(
      By.css('.markdown-content')
    );

    // 验证内容是否存在
    expect(contentElement).toBeTruthy();
  });

  it('should handle empty content', () => {
    // 清除之前的调用记录
    mockMarkdownPipe.transform.mockClear();
    mockAsyncPipe.transform.mockClear();

    // Set input
    component.content = '';
    fixture.detectChanges();

    // 获取 HTML 内容
    const contentElement = fixture.debugElement.query(
      By.css('.markdown-content')
    );

    // 验证内容是否存在
    expect(contentElement).toBeTruthy();
  });
});
