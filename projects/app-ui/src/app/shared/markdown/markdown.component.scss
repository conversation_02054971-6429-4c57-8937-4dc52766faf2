app-markdown > .markdown-content {
  line-height: var(--line-height-base);

  h1, h2, h3, h4, h5, h6 {
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
  }

  h1 {
    font-size: var(--font-size-xl);
  }

  h2 {
    font-size: var(--font-size-lg);
  }

  h3 {
    font-size: var(--font-size-md);
  }

  p {
    margin: var(--spacing-md) 0;
  }

  ul, ol {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-md);
  }

  li {
    margin: var(--spacing-xs) 0;
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);

    &:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
  }

  blockquote {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-md);
    border-left: var(--border-width-thick) solid var(--border-color);
    color: var(--text-secondary);
  }

  code {
    background-color: var(--secondary-color);
    padding: var(--spacing-xxs) var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
  }

  pre {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
    background-color: var(--secondary-color);
    border-radius: var(--border-radius-md);
    overflow-x: auto;

    code {
      background-color: transparent;
      padding: 0;
      border-radius: 0;
      font-size: var(--font-size-sm);
    }
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
  }

  video, audio {
    display: block;
    max-width: 100%;
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-sm);
    background-color: var(--secondary-color);
  }

  video {
    height: auto;
  }

  audio {
    width: 100%;
  }

  hr {
    margin: var(--spacing-xl) 0;
    border: none;
    border-top: var(--border-width-thin) solid var(--border-color);
  }

  [class^="content-renderer-"] {
    position: relative;
    /* 工具栏基础样式 */
    .toolbar {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xxs);
      margin-top: calc(-1 * var(--spacing-xs));
      opacity: 0.5;
      transition: all var(--transition-fast);

      /* 默认状态下工具栏透明度较低，悬停时恢复 */
      &:hover {
        opacity: 1;
      }
    }

    /* 组件悬停时工具栏完全显示 */
    &:hover .toolbar {
      opacity: 1;
    }

    /* 工具按钮基础样式 */
    .toolbar-button {
      width: 24px;
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--background-color);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      border-radius: 50%;
      transition: all var(--transition-fast);
      padding: 0;
      min-width: 0;
      position: relative;

      &:hover {
        background-color: var(--secondary-color);
        box-shadow: var(--shadow-md);
      }

      /* 图标样式 */
      mat-icon {
        --mdc-icon-button-icon-size: 12px;
        font-size: 12px;
        width: 12px;
        height: 12px;
      }
    }

    /* 全屏模式样式覆盖 */
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 1000;
      margin: 0;
      padding: var(--spacing-lg);
      background-color: var(--background-color);
      border-radius: 0;
      border: none;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      /* 全屏模式下工具栏位置和样式覆盖 */
      .toolbar {
        opacity: 1;
      }
    }
  }
}
