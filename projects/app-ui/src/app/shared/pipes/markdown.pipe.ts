import { <PERSON><PERSON>, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import type MarkdownIt from 'markdown-it';
import { subPlugin } from './plugins/markdown-it-sub';
import { supPlugin } from './plugins/markdown-it-sup';
import { katexPlugin } from './plugins/markdown-it-katex';
import { mentionPlugin } from './plugins/markdown-it-mention';
import { admonitionPlugin } from './plugins/markdown-it-admonition';
import { checkboxPlugin } from './plugins/markdown-it-checkbox';
import { codeBlocksPlugin } from './plugins/markdown-it-code-blocks';
import { mediaPlugin } from './plugins/markdown-it-media';
import multiTablePlugin from 'markdown-it-multimd-table';

@Pipe({
  name: 'markdown',
  standalone: true,
})
export class MarkdownPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  static markdownGlobalPrefix = `markdown_${Date.now().toString(36)}`;

  private markdownIt?: MarkdownIt;

  async transform(value: string | null | undefined): Promise<SafeHtml> {
    if (!value) {
      return '';
    }

    if (!this.markdownIt) {
      const MarkdownIt = (await import('markdown-it')).default;

      this.markdownIt = MarkdownIt({
        html: true,
        breaks: true,
        linkify: true,
        typographer: true,
        // 代码高亮逻辑已移到 codeBlocksPlugin 中
        highlight: null,
      })
        .use(multiTablePlugin, {
          rowspan: true,
          headerless: true,
          multibody: true,
          multiline: true,
          autolabel: true,
        })
        .use(subPlugin)
        .use(supPlugin)
        .use(katexPlugin)
        .use(mentionPlugin)
        .use(admonitionPlugin)
        .use(checkboxPlugin, {
          idPrefix: `${MarkdownPipe.markdownGlobalPrefix}_checkbox`,
        })
        // 使用新的统一代码块插件，替代原来的三个插件
        .use(codeBlocksPlugin)
        // 使用媒体插件，支持视频和音频渲染
        .use(mediaPlugin);
    }

    const markdownIt = this.markdownIt;
    const html = markdownIt.render(value);

    // 使用DomSanitizer确保HTML安全
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
