import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

/**
 * 安全 HTML 管道
 *
 * 使用 DomSanitizer 对输入的 HTML 字符串进行安全处理，
 * 防止 XSS 攻击，同时允许显示 HTML 内容。
 *
 * 使用示例：
 * ```html
 * <div [innerHTML]="htmlString | safeHtml"></div>
 * ```
 */
@Pipe({
  name: 'safeHtml',
  standalone: true,
})
export class SafeHtmlPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  /**
   * 将输入的 HTML 字符串转换为安全的 HTML
   * @param value HTML 字符串
   * @returns 安全的 HTML
   */
  transform(value: string): SafeHtml {
    if (!value) {
      return '';
    }
    return this.sanitizer.bypassSecurityTrustHtml(value);
  }
}
