import { TestBed } from '@angular/core/testing';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MarkdownPipe } from './markdown.pipe';

// 使用Jest的模拟功能
describe('MarkdownPipe', () => {
  let pipe: MarkdownPipe;
  let mockSanitizer: { bypassSecurityTrustHtml: jest.Mock };

  beforeEach(() => {
    // 创建模拟的DomSanitizer
    mockSanitizer = {
      bypassSecurityTrustHtml: jest.fn((value) => value as unknown as SafeHtml),
    };

    TestBed.configureTestingModule({
      providers: [
        MarkdownPipe,
        { provide: DomSanitizer, useValue: mockSanitizer },
      ],
    });

    pipe = TestBed.inject(MarkdownPipe);
    MarkdownPipe.markdownGlobalPrefix = 'markdown_test';
    // 每次测试前重置模拟
    jest.clearAllMocks();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return empty string for null input', async () => {
    const result = await pipe.transform(null);
    expect(result).toBe('');
  });

  it('should return empty string for undefined input', async () => {
    const result = await pipe.transform(undefined);
    expect(result).toBe('');
  });

  it('should return empty string for empty input', async () => {
    const result = await pipe.transform('');
    expect(result).toBe('');
  });

  it('should convert newlines to <br> tags', async () => {
    const input = `Line 1
Line 2
Line 3`;
    const expected = `<p>Line 1<br>
Line 2<br>
Line 3</p>
`;

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should convert code blocks with backticks', async () => {
    const input = `Some text
\`\`\`typescript
const x = 1;
console.log(x);
\`\`\`
More text`;
    const result = await pipe.transform(input);
    // 只检查关键部分，因为输出格式可能会变化
    expect(result).toContain('<p>Some text</p>');
    expect(result).toContain('<content-renderer-code');
    expect(result).toContain('language="typescript"');
    expect(result).toContain('code=');
    expect(result).toContain('<p>More text</p>');
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should convert code blocks with backticks and auto detect language', async () => {
    const input = `Some text
\`\`\`
const x = 1;
console.log(x);
\`\`\`
More text`;
    // 由于自动检测语言，我们只检查部分内容
    const result = await pipe.transform(input);
    expect(result).toContain('<p>Some text</p>');
    expect(result).toContain('<content-renderer-code');
    expect(result).toContain('code=');
    // 检查URL编码的内容
    expect(result).toContain('const%20x%20%3D%201');
    expect(result).toContain('console.log(x)');
    expect(result).toContain('<p>More text</p>');
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should handle multiple code blocks', async () => {
    const input = `\`\`\`
Block 1
\`\`\`
Text
\`\`\`
Block 2
\`\`\``;
    // 由于自动检测语言，我们只检查部分内容
    const result = await pipe.transform(input);
    expect(result).toContain('<content-renderer-code');
    // 检查URL编码的内容
    expect(result).toContain('Block%201');
    expect(result).toContain('<p>Text</p>');
    expect(result).toContain('Block%202');
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should handle code blocks with language specifiers', async () => {
    const input = `\`\`\`javascript
const x = 1;
\`\`\``;

    const result = await pipe.transform(input);
    expect(result).toContain('<content-renderer-code');
    expect(result).toContain('language="javascript"');
    expect(result).toContain('code=');
    // 检查URL编码的内容
    expect(result).toContain('const%20x%20%3D%201');
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should handle text with no markdown', async () => {
    const input = 'Just plain text';
    const expected = '<p>Just plain text</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 上标测试
  it('should render superscript text', async () => {
    const input = 'This is a superscript: E=mc^2^';
    const expected = '<p>This is a superscript: E=mc<sup>2</sup></p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render superscript text with multiple characters', async () => {
    const input = 'This is a superscript: 2^10^ equals 1024';
    const expected =
      '<p>This is a superscript: 2<sup>10</sup> equals 1024</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 下标测试
  it('should render subscript text', async () => {
    const input = 'The chemical formula for water is H~2~O';
    const expected =
      '<p>The chemical formula for water is H<sub>2</sub>O</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render subscript text with multiple characters', async () => {
    const input = 'The second element in the sequence is a~n+1~';
    const expected =
      '<p>The second element in the sequence is a<sub>n+1</sub></p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // GFM 表格测试
  it('should render GFM tables', async () => {
    const input = `| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |`;
    const expected = `<table>
<thead>
<tr>
<th>Header 1</th>
<th>Header 2</th>
</tr>
</thead>
<tbody>
<tr>
<td>Cell 1</td>
<td>Cell 2</td>
</tr>
</tbody>
</table>
`;

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render GFM tables with alignment', async () => {
    const input = `| Left | Center | Right |
|:-----|:------:|------:|
| 1    | 2      | 3     |`;
    const expected = `<table>
<thead>
<tr>
<th style="text-align:left">Left</th>
<th style="text-align:center">Center</th>
<th style="text-align:right">Right</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">1</td>
<td style="text-align:center">2</td>
<td style="text-align:right">3</td>
</tr>
</tbody>
</table>
`;

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 自动链接测试
  it('should auto-link URLs', async () => {
    const input = 'Visit https://example.com for more information.';
    const expected =
      '<p>Visit <a href="https://example.com">https://example.com</a> for more information.</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should auto-link email addresses', async () => {
    const input = 'Contact <NAME_EMAIL>';
    const expected =
      '<p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // Mermaid 图表测试
  it('should render Mermaid diagrams', async () => {
    const input = `\`\`\`mermaid
graph TD;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
\`\`\``;
    const expected = `<content-renderer-mermaid code="graph%20TD%3B%0A%20%20%20%20A--%3EB%3B%0A%20%20%20%20A--%3EC%3B%0A%20%20%20%20B--%3ED%3B%0A%20%20%20%20C--%3ED%3B"></content-renderer-mermaid>
`;

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render plantuml sequence diagrams', async () => {
    const input = `\`\`\`plantuml
@startuml
participant Participant as Foo
actor       Actor       as Foo1
boundary    Boundary    as Foo2
control     Control     as Foo3
entity      Entity      as Foo4
database    Database    as Foo5
collections Collections as Foo6
queue       Queue       as Foo7
Foo -> Foo1 : To actor
Foo -> Foo2 : To boundary
Foo -> Foo3 : To control
Foo -> Foo4 : To entity
Foo -> Foo5 : To database
Foo -> Foo6 : To collections
Foo -> Foo7: To queue
@enduml
\`\`\``;
    const expected = `<content-renderer-plantuml code="%40startuml%0Aparticipant%20Participant%20as%20Foo%0Aactor%20%20%20%20%20%20%20Actor%20%20%20%20%20%20%20as%20Foo1%0Aboundary%20%20%20%20Boundary%20%20%20%20as%20Foo2%0Acontrol%20%20%20%20%20Control%20%20%20%20%20as%20Foo3%0Aentity%20%20%20%20%20%20Entity%20%20%20%20%20%20as%20Foo4%0Adatabase%20%20%20%20Database%20%20%20%20as%20Foo5%0Acollections%20Collections%20as%20Foo6%0Aqueue%20%20%20%20%20%20%20Queue%20%20%20%20%20%20%20as%20Foo7%0AFoo%20-%3E%20Foo1%20%3A%20To%20actor%0AFoo%20-%3E%20Foo2%20%3A%20To%20boundary%0AFoo%20-%3E%20Foo3%20%3A%20To%20control%0AFoo%20-%3E%20Foo4%20%3A%20To%20entity%0AFoo%20-%3E%20Foo5%20%3A%20To%20database%0AFoo%20-%3E%20Foo6%20%3A%20To%20collections%0AFoo%20-%3E%20Foo7%3A%20To%20queue%0A%40enduml"></content-renderer-plantuml>
`;

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should support latex(inline)', async () => {
    const input = `$\\sqrt{3x-1}+(1+x)^2$`;
    const expected = `<p><span class="katex"><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:1.04em;vertical-align:-0.1744em;"></span><span class="mord sqrt"><span class="vlist-t vlist-t2"><span class="vlist-r"><span class="vlist" style="height:0.8656em;"><span class="svg-align" style="top:-3em;"><span class="pstrut" style="height:3em;"></span><span class="mord" style="padding-left:0.833em;"><span class="mord">3</span><span class="mord mathnormal">x</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">−</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mord">1</span></span></span><span style="top:-2.8256em;"><span class="pstrut" style="height:3em;"></span><span class="hide-tail" style="min-width:0.853em;height:1.08em;"><svg xmlns="http://www.w3.org/2000/svg" width="400em" height="1.08em" viewBox="0 0 400000 1080" preserveAspectRatio="xMinYMin slice"><path d="M95,702
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l0 -0
c5.3,-9.3,12,-14,20,-14
H400000v40H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M834 80h400000v40h-400000z"/></svg></span></span></span><span class="vlist-s"></span></span><span class="vlist-r"><span class="vlist" style="height:0.1744em;"><span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:1em;vertical-align:-0.25em;"></span><span class="mopen">(</span><span class="mord">1</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:1.0641em;vertical-align:-0.25em;"></span><span class="mord mathnormal">x</span><span class="mclose"><span class="mclose">)</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight">2</span></span></span></span></span></span></span></span></span></span></span></p>
`;
    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should support latex(block)', async () => {
    const input = `$$ \\frac{n!}{k!(n-k)!} = \\binom{n}{k} $$`;
    const expected = `<span class="katex-display"><span class="katex"><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:2.3074em;vertical-align:-0.936em;"></span><span class="mord"><span class="mopen nulldelimiter"></span><span class="mfrac"><span class="vlist-t vlist-t2"><span class="vlist-r"><span class="vlist" style="height:1.3714em;"><span style="top:-2.314em;"><span class="pstrut" style="height:3em;"></span><span class="mord"><span class="mord mathnormal" style="margin-right:0.03148em;">k</span><span class="mclose">!</span><span class="mopen">(</span><span class="mord mathnormal">n</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">−</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mord mathnormal" style="margin-right:0.03148em;">k</span><span class="mclose">)!</span></span></span><span style="top:-3.23em;"><span class="pstrut" style="height:3em;"></span><span class="frac-line" style="border-bottom-width:0.04em;"></span></span><span style="top:-3.677em;"><span class="pstrut" style="height:3em;"></span><span class="mord"><span class="mord mathnormal">n</span><span class="mclose">!</span></span></span></span><span class="vlist-s"></span></span><span class="vlist-r"><span class="vlist" style="height:0.936em;"><span></span></span></span></span></span><span class="mclose nulldelimiter"></span></span><span class="mspace" style="margin-right:0.2778em;"></span><span class="mrel">=</span><span class="mspace" style="margin-right:0.2778em;"></span></span><span class="base"><span class="strut" style="height:2.4em;vertical-align:-0.95em;"></span><span class="mord"><span class="mopen delimcenter" style="top:0em;"><span class="delimsizing size3">(</span></span><span class="mfrac"><span class="vlist-t vlist-t2"><span class="vlist-r"><span class="vlist" style="height:1.1076em;"><span style="top:-2.314em;"><span class="pstrut" style="height:3em;"></span><span class="mord"><span class="mord mathnormal" style="margin-right:0.03148em;">k</span></span></span><span style="top:-3.677em;"><span class="pstrut" style="height:3em;"></span><span class="mord"><span class="mord mathnormal">n</span></span></span></span><span class="vlist-s"></span></span><span class="vlist-r"><span class="vlist" style="height:0.686em;"><span></span></span></span></span></span><span class="mclose delimcenter" style="top:0em;"><span class="delimsizing size3">)</span></span></span></span></span></span></span>`;
    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 提及功能测试
  it('should render mentions', async () => {
    const input = '你好 @username，欢迎加入讨论！';
    const expected =
      '<p>你好 <span class="mention">@username</span>，欢迎加入讨论！</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 提及功能测试
  it('should render mentions(中文)', async () => {
    const input = '你好 @汪志成，欢迎加入讨论！';
    const expected =
      '<p>你好 <span class="mention">@汪志成</span>，欢迎加入讨论！</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render multiple mentions', async () => {
    const input = '@user1 和 @user2 请查看这个问题';
    const expected =
      '<p><span class="mention">@user1</span> 和 <span class="mention">@user2</span> 请查看这个问题</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should not render mentions that are part of email addresses', async () => {
    const input = '请联系 <EMAIL>';
    const expected =
      '<p>请联系 <a href="mailto:<EMAIL>"><EMAIL></a></p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  it('should render mentions with underscores and hyphens', async () => {
    const input = '请 @user_name-123 查看这个问题';
    const expected =
      '<p>请 <span class="mention">@user_name-123</span> 查看这个问题</p>\n';

    const result = await pipe.transform(input);
    expect(result).toBe(expected);
    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(
      expected
    );
  });

  // 警示块功能测试
  it('should render admonition blocks', async () => {
    const input = `!!! note
    这是一个注意事项。

    包含多个段落。`;

    const result = await pipe.transform(input);

    // 检查是否包含警示块的基本结构
    expect(result).toContain('<div class="admonition admonition-note">');
    expect(result).toContain('<div class="admonition-title">');
    expect(result).toContain('<span class="admonition-icon">ℹ️</span>');
    expect(result).toContain('<div class="admonition-content">');
    expect(result).toContain('<p>这是一个注意事项。</p>');
    expect(result).toContain('<p>包含多个段落。</p>');

    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should render admonition blocks with custom title', async () => {
    const input = `!!! warning "自定义警告标题"
    这是一个警告信息。`;

    const result = await pipe.transform(input);

    // 检查是否包含警示块的基本结构和自定义标题
    expect(result).toContain('<div class="admonition admonition-warning">');
    expect(result).toContain('<div class="admonition-title">');
    expect(result).toContain('<span class="admonition-icon">⚠️</span>');
    expect(result).toContain('自定义警告标题');
    expect(result).toContain('<div class="admonition-content">');
    expect(result).toContain('<p>这是一个警告信息。</p>');

    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should render different types of admonition blocks', async () => {
    const input = `!!! tip
    这是一个提示。

!!! info
    这是一个信息。

!!! danger
    这是一个危险警告。`;

    const result = await pipe.transform(input);

    // 检查是否包含不同类型的警示块
    expect(result).toContain('<div class="admonition admonition-tip">');
    expect(result).toContain('<div class="admonition admonition-info">');
    expect(result).toContain('<div class="admonition admonition-danger">');

    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should render check list', async () => {
    const input = `[ ] unchecked
[x] checked`;

    const result = await pipe.transform(input);

    // 检查是否包含不同类型的警示块
    expect(result)
      .toEqual(`<p><input type="checkbox" id="markdown_test_checkbox_rt"><label for="markdown_test_checkbox_rt">unchecked</label><br>
<input type="checkbox" id="markdown_test_checkbox_rs" checked="checked"><label for="markdown_test_checkbox_rs">checked</label></p>
`);

    expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalled();
  });

  it('should render multimd table', async () => {
    const input = `Stage | Direct Products | ATP Yields
----: | --------------: | ---------:
Glycolysis | 2 ATP ||
^^ | 2 NADH | 3--5 ATP |
Pyruvaye oxidation | 2 NADH | 5 ATP |
Citric acid cycle | 2 ATP ||
^^ | 6 NADH | 15 ATP |
^^ | 2 FADH2 | 3 ATP |
**30--32** ATP |||
[Net ATP yields per hexose]`;
    const expected = `<table>
<caption id="netatpyieldsperhexose" style="caption-side: bottom">Net ATP yields per hexose</caption>
<thead>
<tr>
<th style="text-align:right">Stage</th>
<th style="text-align:right">Direct Products</th>
<th style="text-align:right">ATP Yields</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:right" rowspan="2">Glycolysis</td>
<td style="text-align:right" colspan="2">2 ATP</td>
</tr>
<tr>
<td style="text-align:right">2 NADH</td>
<td style="text-align:right">3–5 ATP</td>
</tr>
<tr>
<td style="text-align:right">Pyruvaye oxidation</td>
<td style="text-align:right">2 NADH</td>
<td style="text-align:right">5 ATP</td>
</tr>
<tr>
<td style="text-align:right" rowspan="3">Citric acid cycle</td>
<td style="text-align:right" colspan="2">2 ATP</td>
</tr>
<tr>
<td style="text-align:right">6 NADH</td>
<td style="text-align:right">15 ATP</td>
</tr>
<tr>
<td style="text-align:right">2 FADH2</td>
<td style="text-align:right">3 ATP</td>
</tr>
<tr>
<td style="text-align:right" colspan="3"><strong>30–32</strong> ATP</td>
</tr>
</tbody>
</table>
`;
    const result = await pipe.transform(input);

    // 检查是否包含不同类型的警示块
    expect(result).toEqual(expected);
  });
});
