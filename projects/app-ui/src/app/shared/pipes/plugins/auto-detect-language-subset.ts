/**
 * 自动检测语言的子集，按照出现频率从高到低排序
 * 这个列表将用于限制 highlight.js 的自动检测范围，提高准确性和性能
 */
export const autoDetectLanguageSubset = [
  // 前端语言
  'javascript', // JavaScript
  'typescript', // TypeScript
  'html', // HTML
  'css', // CSS
  'less', // LESS
  'jsx', // React JSX
  'tsx', // React TSX
  'vue', // Vue

  // 后端语言
  'python', // Python
  'java', // Java
  'csharp', // C#
  'php', // PHP
  'ruby', // <PERSON>
  'go', // Go
  'rust', // Rust
  'kotlin', // Kotlin
  'swift', // <PERSON>
  'scala', // Scala

  // 数据相关
  'json', // JSON
  'yaml', // YAML
  'xml', // XML
  'sql', // SQL
  'graphql', // GraphQL

  // 脚本和配置
  'bash', // Bash
  'powershell', // PowerShell
  'dockerfile', // Dockerfile

  // 低级语言
  'c', // C
  'cpp', // C++
  'objectivec', // Objective-C

  // 其他常见语言
  'markdown', // Markdown
  'diff', // Diff
  'ini', // INI
  'toml', // TOML
];
