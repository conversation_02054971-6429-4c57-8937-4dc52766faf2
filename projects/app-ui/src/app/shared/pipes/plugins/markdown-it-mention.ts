import {default as MarkdownIt, StateInline, Token} from 'markdown-it/index.js';

// 匹配提及的正则表达式
const MENTION_RE = /^@([a-zA-Z0-9_\p{sc=Han}-]+)/u;

/**
 * 处理提及语法的内联规则函数
 * 格式: @username
 */
function mentionRule(state: StateInline, silent: boolean): boolean {
  // 不在行首位置才检查前一个字符
  if (state.pos > 0) {
    // 如果 @ 前面不是空白字符或行首，则不触发提及规则
    const prevChar = state.src.charCodeAt(state.pos - 1);
    if (
      prevChar !== 0x20 && // 空格
      prevChar !== 0x09 && // 制表符
      prevChar !== 0x0a && // 换行符
      prevChar !== 0x0d // 回车符
    ) {
      return false;
    }
  }

  // 检查当前字符是否为 @
  if (state.src.charCodeAt(state.pos) !== 0x40 /* @ */) {
    return false;
  }

  // 尝试匹配提及格式
  const match = MENTION_RE.exec(state.src.slice(state.pos));
  if (!match) {
    return false;
  }

  const username = match[1];

  // 如果在验证模式下，直接返回成功
  if (silent) {
    return true;
  }

  // 创建提及的token
  const token = state.push('mention', '', 0);
  token.meta = { username };
  token.content = username;

  // 更新解析位置
  state.pos += match[0].length;

  return true;
}

/**
 * 渲染提及的函数
 */
function renderMention(tokens: Token[], idx: number): string {
  const token = tokens[idx];
  const username = token.meta?.username || token.content;

  // 返回带有特殊样式的提及标记
  return `<span class="mention">@${username}</span>`;
}

/**
 * Markdown-it 提及插件
 */
export function mentionPlugin(md: MarkdownIt): void {
  // 添加提及规则到内联规则中
  md.inline.ruler.after('emphasis', 'mention', mentionRule);

  // 添加提及的渲染规则
  md.renderer.rules['mention'] = renderMention;
}
