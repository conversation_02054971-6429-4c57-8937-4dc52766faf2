import {default as MarkdownIt, StateInline, Token} from 'markdown-it/index.js';

/**
 * 统一的代码块处理插件，整合了以下功能：
 * 1. Mermaid 图表渲染
 * 2. PlantUML 图表渲染
 * 3. 代码语言标签显示
 * 4. 代码高亮处理
 *
 * @param md markdown-it 实例
 */
export function codeBlocksPlugin(md: MarkdownIt): void {
  // 保存原始的 fence 渲染规则
  const defaultFenceRender = md.renderer.rules.fence;

  // 添加自定义 fence 渲染规则
  md.renderer.rules.fence = (
    tokens: Token[],
    idx: number,
    options: any,
    env: any,
    self: any
  ): string => {
    const token = tokens[idx];
    const info = token.info ? md.utils.unescapeAll(token.info).trim() : '';
    const lang = info.split(/\s+/g)[0];
    const content = token.content;

    // 处理 Mermaid 图表
    if (lang === 'mermaid') {
      return `<content-renderer-mermaid code="${encodeURIComponent(
        content.trim()
      )}"></content-renderer-mermaid>
`;
    }

    // 处理 PlantUML 图表
    if (lang === 'plantuml' || lang === 'puml') {
      return `<content-renderer-plantuml code="${encodeURIComponent(
        content.trim()
      )}"></content-renderer-plantuml>
`;
    }

    return `<content-renderer-code language="${lang}" code="${encodeURIComponent(
      content.trim()
    )}"></content-renderer-code>`;
  };
}
