import {default as MarkdownIt, StateInline, Token, StateCore} from 'markdown-it/index.js';

// 定义插件选项的接口
interface CheckboxOptions {
  /** Whether to wrap the checkbox and label in a div */
  divWrap: boolean;
  /** CSS class for the wrapping div if divWrap is true */
  divClass: string;
  /** Prefix for the generated input and label IDs */
  idPrefix: string;
}

// 定义一个类型别名，表示 markdown-it Token 的构造函数
type MarkdownItTokenConstructor = new (
  type: string,
  tag: string,
  nesting: MarkdownIt.Token.Nesting
) => Token;

// 用于生成唯一ID的计数器，所有插件实例共享
let checkboxIdCounter = 1000;

// markdown-it 插件入口函数
export function checkboxPlugin(
  md: MarkdownIt,
  options?: Partial<CheckboxOptions>
) {
  // 默认选项
  const defaultOptions: CheckboxOptions = {
    divWrap: false,
    divClass: 'markdown_checkbox',
    idPrefix: 'checkbox_' + Date.now().toString(36),
  };

  // 合并用户提供的选项和默认选项
  const pluginOptions: CheckboxOptions = Object.assign(
    {},
    defaultOptions,
    options
  );

  // 匹配 checkbox 模式的正则表达式
  const checkboxPattern = /\[(X|\s|_|-)]\s(.*)/i;

  /**
   * 根据匹配到的内容创建 markdown-it Tokens，代表 HTML 结构
   * @param isChecked - 复选框是否选中
   * @param labelText - 复选框后面的文本
   * @param TokenClass - markdown-it 的 Token 类构造函数
   * @param options - 当前插件实例的配置选项
   * @returns 代表复选框 HTML 结构的 Token 数组
   */
  const buildCheckboxHtmlTokens = (
    isChecked: boolean,
    labelText: string,
    TokenClass: MarkdownItTokenConstructor, // 修正类型为 typeof Token
    options: CheckboxOptions
  ): Token[] => {
    const tokens: Token[] = [];
    let currentToken: Token;

    // 生成唯一的 ID
    const uniqueId = `${options.idPrefix}_${checkboxIdCounter.toString(36)}`;
    checkboxIdCounter += 1; // 每次生成 tokens 都要递增 ID

    // 可选的 div 包裹层
    if (options.divWrap) {
      currentToken = new TokenClass('checkbox_open', 'div', 1);
      currentToken.attrs = [['class', options.divClass]];
      tokens.push(currentToken);
    }

    // input[type="checkbox"]
    currentToken = new TokenClass('checkbox_input', 'input', 0);
    currentToken.attrs = [
      ['type', 'checkbox'],
      ['id', uniqueId],
    ];
    if (isChecked) {
      currentToken.attrs.push(['checked', 'checked']);
    }
    tokens.push(currentToken);

    // label
    currentToken = new TokenClass('label_open', 'label', 1);
    currentToken.attrs = [['for', uniqueId]];
    tokens.push(currentToken);

    // label content (text)
    currentToken = new TokenClass('text', '', 0);
    currentToken.content = labelText;
    tokens.push(currentToken);

    // closing label tag
    tokens.push(new TokenClass('label_close', 'label', -1));

    // 可选的 closing div tag
    if (options.divWrap) {
      tokens.push(new TokenClass('checkbox_close', 'div', -1));
    }

    return tokens;
  };

  /**
   * 检查 Token 内容是否匹配 checkbox 模式，并生成替换用的 Tokens
   * @param originalTextToken - 原始的文本 Token
   * @param TokenClass - markdown-it 的 Token 类构造函数
   * @param options - 当前插件实例的配置选项
   * @returns 替换用的 Token 数组 (包含原始 Token 或新生成的 HTML Tokens)
   */
  const processPotentialCheckboxToken = (
    originalTextToken: Token,
    TokenClass: MarkdownItTokenConstructor, // 修正类型为 typeof Token
    options: CheckboxOptions
  ): Token[] => {
    const textContent = originalTextToken.content;
    const match = textContent.match(checkboxPattern);

    // 如果不匹配模式，直接返回包含原始 Token 的数组，以便 arrayReplaceAt 替换自身
    if (!match) {
      return [originalTextToken];
    }

    // 提取匹配到的内容
    const checkboxValue = match[1]; // X, _, -, or space
    const labelText = match[2]; // Checkbox text after []

    // 判断是否选中
    const isChecked = checkboxValue === 'X' || checkboxValue === 'x';

    // 生成新的 Tokens
    return buildCheckboxHtmlTokens(isChecked, labelText, TokenClass, options);
  };

  // markdown-it core 规则函数
  const applyCheckboxTransformation = (state: StateCore) => {
    const blockTokens = state.tokens;
    const arrayReplaceAt = md.utils.arrayReplaceAt;

    // 遍历块级 Tokens
    for (let blockIndex = 0; blockIndex < blockTokens.length; blockIndex++) {
      const blockToken = blockTokens[blockIndex];

      // 只处理 inline 类型的 Token，并且确保它有子 Token
      if (blockToken.type !== 'inline' || !blockToken.children) {
        continue;
      }

      let inlineTokens = blockToken.children;

      // 从后向前遍历 inline Token 的子 Token，处理 text 类型
      for (
        let inlineIndex = inlineTokens.length - 1;
        inlineIndex >= 0;
        inlineIndex--
      ) {
        const inlineToken = inlineTokens[inlineIndex];

        // 只处理文本 Token
        if (inlineToken.type === 'text') {
          const replacementTokens = processPotentialCheckboxToken(
            inlineToken,
            state.Token, // 获取当前状态下的 Token 类
            pluginOptions // 使用合并后的插件选项
          );

          // 使用 arrayReplaceAt 替换原有的文本 Token
          // 如果 replacementTokens 只有一个元素 (原始 Token)，则原地替换
          // 如果是多个元素 (HTML Tokens)，则展开替换
          blockToken.children = inlineTokens = arrayReplaceAt(
            inlineTokens,
            inlineIndex,
            replacementTokens
          ) as Token[]; // 明确类型转换
        }
        // 非 text token 被忽略
      }
    }
  };

  // 将 core 规则添加到 markdown-it 的规则链中
  md.core.ruler.push('checkbox', applyCheckboxTransformation);
}
