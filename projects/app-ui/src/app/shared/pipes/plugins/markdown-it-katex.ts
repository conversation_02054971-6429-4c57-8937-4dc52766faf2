import {default as MarkdownIt, StateInline, Token} from 'markdown-it/index.js';
import { renderToString } from 'katex';

// 定义 LaTeX 分隔符
const INLINE_MATH_DELIMITERS = [
  { left: '\\(', right: '\\)', display: false },
  { left: '$', right: '$', display: false },
];

const BLOCK_MATH_DELIMITERS = [
  { left: '\\[', right: '\\]', display: true },
  { left: '$$', right: '$$', display: true },
];

// 用于匹配 LaTeX 分隔符的正则表达式
function escapeRegExp(str: string): string {
  return str.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
}

/**
 * 创建用于匹配 LaTeX 内容的正则表达式
 * @param delimiters 分隔符配置
 * @returns 正则表达式
 */
function createMathRegex(
  delimiters: { left: string; right: string; display: boolean }[]
): RegExp {
  const patterns = delimiters.map((delimiter) => {
    const leftDelim = escapeRegExp(delimiter.left);
    const rightDelim = escapeRegExp(delimiter.right);
    return `${leftDelim}(.*?)${rightDelim}`;
  });
  return new RegExp(patterns.join('|'), 'g');
}

/**
 * 渲染 LaTeX 公式
 * @param content LaTeX 内容
 * @param displayMode 是否为块级显示
 * @returns 渲染后的 HTML
 */
function renderKatex(content: string, displayMode: boolean): string {
  try {
    return renderToString(content, {
      displayMode,
      throwOnError: false,
      output: 'html',
    }).replace(/\u200B/g, '');
  } catch (error) {
    console.error('KaTeX 渲染错误:', error);
    return `<span class="katex-error" title="${error}">LaTeX 渲染错误: ${content}</span>`;
  }
}

/**
 * markdown-it 插件，用于处理 LaTeX 数学公式
 * @param md markdown-it 实例
 */
export function katexPlugin(md: MarkdownIt): void {
  // 保存原始的 text 渲染规则
  const originalTextRenderer =
    md.renderer.rules.text || ((tokens, idx) => tokens[idx].content);

  // 重写 text 渲染规则，处理内联 LaTeX
  md.renderer.rules.text = (
    tokens: Token[],
    idx: number,
    options: any,
    env: any,
    self: any
  ): string => {
    const token = tokens[idx];
    const content = token.content;

    // 创建内联数学正则表达式
    const inlineMathRegex = createMathRegex(INLINE_MATH_DELIMITERS);

    // 如果没有匹配到 LaTeX 内容，使用原始渲染器
    if (!inlineMathRegex.test(content)) {
      return originalTextRenderer(tokens, idx, options, env, self);
    }

    // 重置正则表达式的 lastIndex
    inlineMathRegex.lastIndex = 0;

    // 替换所有匹配的 LaTeX 内容
    let result = content;
    let match;

    while ((match = inlineMathRegex.exec(content)) !== null) {
      const fullMatch = match[0];
      // 找到匹配的分隔符
      const delimiter = INLINE_MATH_DELIMITERS.find(
        (d) => fullMatch.startsWith(d.left) && fullMatch.endsWith(d.right)
      );

      if (delimiter) {
        // 提取 LaTeX 内容（去除分隔符）
        const latex = fullMatch.slice(
          delimiter.left.length,
          fullMatch.length - delimiter.right.length
        );

        // 渲染 LaTeX
        const rendered = renderKatex(latex, delimiter.display);

        // 替换结果中的 LaTeX
        result = result.replace(fullMatch, rendered);
      }
    }

    return result;
  };

  // 添加块级 LaTeX 支持
  const blockMathRule = (
    state: any,
    startLine: number,
    endLine: number,
    silent: boolean
  ): boolean => {
    let pos = state.bMarks[startLine] + state.tShift[startLine];
    let max = state.eMarks[startLine];
    let line = state.src.slice(pos, max);

    // 检查是否是块级 LaTeX 开始
    const delimiter = BLOCK_MATH_DELIMITERS.find((d) =>
      line.trim().startsWith(d.left)
    );
    if (!delimiter) return false;

    // 如果是验证模式，直接返回成功
    if (silent) return true;

    // 查找结束分隔符
    let nextLine = startLine;
    let found = false;
    let latex = '';

    // 如果开始和结束分隔符在同一行
    if (line.trim().endsWith(delimiter.right)) {
      latex = line
        .trim()
        .slice(
          delimiter.left.length,
          line.trim().length - delimiter.right.length
        );
      found = true;
    } else {
      // 查找多行 LaTeX
      latex = line.trim().slice(delimiter.left.length) + '\n';

      for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
        pos = state.bMarks[nextLine] + state.tShift[nextLine];
        max = state.eMarks[nextLine];
        line = state.src.slice(pos, max);

        if (line.trim().endsWith(delimiter.right)) {
          latex += line
            .trim()
            .slice(0, line.trim().length - delimiter.right.length);
          found = true;
          break;
        } else {
          latex += line + '\n';
        }
      }
    }

    if (!found) return false;

    // 更新 state
    state.line = nextLine + 1;

    // 创建 token
    const token = state.push('katex_block', '', 0);
    token.content = latex;
    token.block = true;
    token.info = 'katex';
    token.map = [startLine, nextLine + 1];

    return true;
  };

  // 添加块级 LaTeX 渲染规则
  md.renderer.rules['katex_block'] = (tokens: Token[], idx: number): string => {
    return renderKatex(tokens[idx].content, true);
  };

  // 注册块级规则
  md.block.ruler.before('code', 'katex_block', blockMathRule);
}
