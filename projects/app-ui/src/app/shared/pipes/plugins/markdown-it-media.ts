import {default as MarkdownIt, Token} from 'markdown-it/index.js';

/**
 * 媒体文件扩展名映射
 */
const MEDIA_EXTENSIONS = {
  // 视频文件扩展名
  video: ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'm4v'],
  // 音频文件扩展名
  audio: ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'],
};

/**
 * 检查URL是否为视频文件
 * @param url 文件URL
 * @returns 是否为视频文件
 */
function isVideoFile(url: string): boolean {
  const extension = getFileExtension(url);
  return MEDIA_EXTENSIONS.video.includes(extension.toLowerCase());
}

/**
 * 检查URL是否为音频文件
 * @param url 文件URL
 * @returns 是否为音频文件
 */
function isAudioFile(url: string): boolean {
  const extension = getFileExtension(url);
  return MEDIA_EXTENSIONS.audio.includes(extension.toLowerCase());
}

/**
 * 获取文件扩展名
 * @param url 文件URL
 * @returns 文件扩展名
 */
function getFileExtension(url: string): string {
  // 移除URL参数
  const baseUrl = url.split('?')[0].split('#')[0];
  // 获取最后一个点后面的内容作为扩展名
  const extension = baseUrl.split('.').pop() || '';
  return extension;
}

/**
 * 解析属性字符串
 * 格式: width=100 height=200 controls autoplay
 * @param attrString 属性字符串
 * @returns 属性对象
 */
function parseAttributes(attrString: string): Record<string, string> {
  const attrs: Record<string, string> = {};

  if (!attrString) return attrs;

  // 匹配 key=value 或单独的 key
  const attrRegex = /(\w+)(?:=([^\s]+))?/g;
  let match;

  while ((match = attrRegex.exec(attrString)) !== null) {
    const [, key, value] = match;
    attrs[key] = value || '';
  }

  return attrs;
}

/**
 * 从alt文本中提取属性
 * 格式: alt text {width=100 height=200 controls autoplay}
 * @param alt alt文本
 * @returns [纯alt文本, 属性对象]
 */
function extractAttributesFromAlt(
    alt: string,
): [string, Record<string, string>] {
  const attrRegex = /\s*\{([^}]+)\}\s*$/;
  const match = alt.match(attrRegex);

  if (match) {
    const attrString = match[1];
    const cleanAlt = alt.replace(attrRegex, '').trim();
    return [cleanAlt, parseAttributes(attrString)];
  }

  return [alt, {}];
}

/**
 * 渲染视频元素
 * @param url 视频URL
 * @param alt alt文本
 * @param title 标题
 * @param attrs 额外属性
 * @returns HTML字符串
 */
function renderVideo(
    url: string,
    alt: string,
    title: string,
    attrs: Record<string, string>,
): string {
  const [cleanAlt, customAttrs] = extractAttributesFromAlt(alt);

  // 合并属性
  const mergedAttrs = {...customAttrs, ...attrs};

  // 构建属性字符串
  let attrString = '';
  for (const [key, value] of Object.entries(mergedAttrs)) {
    if (value === '') {
      // 布尔属性
      attrString += ` ${key}`;
    } else {
      attrString += ` ${key}="${value}"`;
    }
  }

  // 添加默认控制条
  if (!('controls' in mergedAttrs)) {
    attrString += ' controls';
  }

  return `<content-renderer-video videoUrl="${url}">
    <video src="${url}"${attrString} title="${title || cleanAlt}">
      ${cleanAlt || '您的浏览器不支持视频播放'}
    </video>
  </content-renderer-video>`;
}

/**
 * 渲染音频元素
 * @param url 音频URL
 * @param alt alt文本
 * @param title 标题
 * @param attrs 额外属性
 * @returns HTML字符串
 */
function renderAudio(
    url: string,
    alt: string,
    title: string,
    attrs: Record<string, string>,
): string {
  const [cleanAlt, customAttrs] = extractAttributesFromAlt(alt);

  // 合并属性
  const mergedAttrs = {...customAttrs, ...attrs};

  // 构建属性字符串
  let attrString = '';
  for (const [key, value] of Object.entries(mergedAttrs)) {
    if (value === '') {
      // 布尔属性
      attrString += ` ${key}`;
    } else {
      attrString += ` ${key}="${value}"`;
    }
  }

  // 添加默认控制条
  if (!('controls' in mergedAttrs)) {
    attrString += ' controls';
  }

  return `<content-renderer-audio audioUrl="${url}">
    <audio src="${url}"${attrString} title="${title || cleanAlt}">
      ${cleanAlt || '您的浏览器不支持音频播放'}
    </audio>
  </content-renderer-audio>`;
}

/**
 * Markdown-it 媒体插件
 * 扩展图片语法以支持视频和音频
 * @param md markdown-it实例
 */
export function mediaPlugin(md: MarkdownIt): void {
  // 保存原始的图片渲染规则
  const defaultImageRender =
      md.renderer.rules.image ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options);
      };

  // 重写图片渲染规则
  md.renderer.rules.image = (
      tokens: Token[],
      idx: number,
      options: any,
      env: any,
      self: any,
  ): string => {
    const token = tokens[idx];
    const srcIndex = token.attrIndex('src');
    const src = token.attrs![srcIndex][1];
    const altIndex = token.attrIndex('alt');
    const alt = altIndex >= 0 ? token.attrs![altIndex][1] : '';

    // 查找title属性
    let title = '';
    for (let i = 0; i < (token.attrs?.length ?? 0); i++) {
      if (token.attrs?.[i][0] === 'title') {
        title = token.attrs?.[i][1];
        break;
      }
    }

    // 根据文件扩展名决定渲染方式
    if (isVideoFile(src)) {
      return renderVideo(src, alt, title, {});
    } else if (isAudioFile(src)) {
      return renderAudio(src, alt, title, {});
    }

    // 如果不是媒体文件，使用默认渲染并包装
    const defaultHtml = defaultImageRender(tokens, idx, options, env, self);
    return `<content-renderer-image imageUrl="${src}">
      ${defaultHtml}
    </content-renderer-image>`;
  };
}
