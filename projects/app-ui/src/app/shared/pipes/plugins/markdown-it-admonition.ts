import {default as MarkdownIt, StateBlock} from 'markdown-it/index.js';

// 警示块类型及其图标和标题
const ADMONITION_TYPES: Record<string, { title: string; icon: string }> = {
  note: {title: '注意', icon: 'ℹ️'},
  tip: {title: '提示', icon: '💡'},
  info: {title: '信息', icon: 'ℹ️'},
  warning: {title: '警告', icon: '⚠️'},
  danger: {title: '危险', icon: '🚫'},
  error: {title: '错误', icon: '❌'},
  important: {title: '重要', icon: '❗'},
  success: {title: '成功', icon: '✅'},
  question: {title: '问题', icon: '❓'},
  example: {title: '示例', icon: '📝'},
  quote: {title: '引用', icon: '💬'},
  bug: {title: '缺陷', icon: '🐞'},
  abstract: {title: '摘要', icon: '📋'},
  todo: {title: '待办', icon: '📌'},
};

/**
 * 解析警示块的规则
 * 格式:
 * !!! type "可选标题"
 *     内容行1
 *     内容行2
 */
function admonitionRule(
    state: StateBlock,
    startLine: number,
    endLine: number,
    silent: boolean,
): boolean {
  // 如果在静默模式下，不进行实际解析
  if (silent) {
    return false;
  }

  const start = state.bMarks[startLine] + state.tShift[startLine];
  const max = state.eMarks[startLine];

  // 检查开头是否为 !!!
  if (
      max - start < 3 ||
      state.src.charCodeAt(start) !== 0x21 /* ! */ ||
      state.src.charCodeAt(start + 1) !== 0x21 /* ! */ ||
      state.src.charCodeAt(start + 2) !== 0x21 /* ! */
  ) {
    return false;
  }

  // 获取第一行内容
  const firstLine = state.src.slice(start, max).trim();

  // 解析警示块类型和标题
  const admonitionMatch = firstLine.match(/^!!!\s+(\w+)(?:\s+"([^"]+)")?/);
  if (!admonitionMatch) {
    return false;
  }

  const type = admonitionMatch[1].toLowerCase();
  const customTitle = admonitionMatch[2] || '';

  // 检查是否是支持的警示块类型
  if (!ADMONITION_TYPES[type]) {
    return false;
  }

  // 查找警示块内容（缩进的行）
  let nextLine = startLine + 1;
  let content = '';

  // 跳过空行
  while (nextLine < endLine && state.isEmpty(nextLine)) {
    nextLine++;
  }

  // 收集缩进的内容行
  while (nextLine < endLine) {
    const lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
    const lineMax = state.eMarks[nextLine];

    // 检查缩进
    if (state.sCount[nextLine] - state.blkIndent < 4) {
      // 如果缩进不足，检查是否是空行
      if (state.isEmpty(nextLine)) {
        // 空行也添加到内容中，以保持段落分隔
        content += '\n';
        nextLine++;
        continue;
      }
      // 如果不是空行且缩进不足，则结束警示块
      break;
    }

    // 添加内容行（去除缩进）
    content += state.src.slice(lineStart, lineMax) + '\n';
    nextLine++;
  }

  // 更新状态
  state.line = nextLine;

  // 创建警示块的token
  const token = state.push('admonition_open', 'div', 1);
  token.attrSet('class', `admonition admonition-${type}`);
  token.map = [startLine, nextLine];

  // 创建标题token
  const titleToken = state.push('admonition_title_open', 'div', 1);
  titleToken.attrSet('class', 'admonition-title');

  // 创建图标token
  const iconToken = state.push('html_inline', '', 0);
  iconToken.content = `<span class="admonition-icon">${ADMONITION_TYPES[type].icon}</span>`;

  // 创建标题文本token
  const titleTextToken = state.push('text', '', 0);
  titleTextToken.content = customTitle || ADMONITION_TYPES[type].title;

  // 关闭标题
  state.push('admonition_title_close', 'div', -1);

  // 创建内容容器
  const contentToken = state.push('admonition_content_open', 'div', 1);
  contentToken.attrSet('class', 'admonition-content');

  // 解析内容
  state.md.block.parse(content, state.md, state.env, state.tokens);

  // 关闭内容容器
  state.push('admonition_content_close', 'div', -1);

  // 关闭警示块
  state.push('admonition_close', 'div', -1);

  return true;
}

/**
 * 警示块插件
 */
export function admonitionPlugin(md: MarkdownIt): void {
  // 添加警示块规则
  md.block.ruler.before('fence', 'admonition', admonitionRule);

  // 添加渲染规则
  md.renderer.rules['admonition_open'] = (tokens, idx) => {
    return `<div class="${tokens[idx].attrGet('class')}">`;
  };

  md.renderer.rules['admonition_title_open'] = (tokens, idx) => {
    return `<div class="${tokens[idx].attrGet('class')}">`;
  };

  md.renderer.rules['admonition_content_open'] = (tokens, idx) => {
    return `<div class="${tokens[idx].attrGet('class')}">`;
  };

  md.renderer.rules['admonition_title_close'] = () => '</div>';
  md.renderer.rules['admonition_content_close'] = () => '</div>';
  md.renderer.rules['admonition_close'] = () => '</div>';
}
