// same as UNESCAPE_MD_RE plus a space

import {default as MarkdownIt, StateInline, Token} from 'markdown-it/index.js';

// 定义正则表达式并指定类型
const UNESCAPE_RE = /\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;

// 定义 superscript 内联规则函数，并指定参数和返回值的类型
function superscript(state: StateInline, silent: boolean): boolean {
  const max: number = state.posMax;
  const start: number = state.pos;

  // 触发字符必须是 '^'
  if (state.src.charCodeAt(start) !== 0x5e /* ^ */) {
    return false;
  }

  // 如果在 silent (validation) 模式下，直接返回 false，不进行实际解析
  // (原始代码中的 !silent 判断在此处更直观)
  if (silent) {
    // 即使 silent 模式下，规则也可能需要消耗字符以避免无限循环。
    // 原始代码在 silent 模式下直接返回 false，可能不完全符合 markdown-it 规则的要求，
    // 严格来说，silent 模式下如果匹配成功，应该更新 state.pos 并返回 true。
    // 但为了保持与原 JS 代码逻辑一致，我们保留原逻辑。
    return false;
  }

  // 检查是否有至少一个字符在 '^' 后面（`^a^` 是最小有效结构）
  if (start + 2 >= max) {
    return false;
  }

  // 从 '^' 的下一个位置开始查找匹配的 '^'
  state.pos = start + 1;
  let found = false;

  while (state.pos < max) {
    // 找到匹配的结束 '^'
    if (state.src.charCodeAt(state.pos) === 0x5e /* ^ */) {
      found = true;
      break;
    }

    // 跳过当前位置的 token，处理转义等情况
    state.md.inline.skipToken(state);
  }

  // 如果没有找到结束 '^'，或者开始和结束标记之间没有内容，则匹配失败
  if (!found || start + 1 === state.pos) {
    state.pos = start; // 恢复 pos
    return false;
  }

  // 提取上标内容（不包括首尾的 '^'）
  const content: string = state.src.slice(start + 1, state.pos);

  // 检查内容中是否包含未转义的空格或换行符
  // (原始代码中的正则表达式，需要注意转义斜杠 '\\\\')
  // GFM 上标插件通常不允许内部有空格，但这个正则检查的是未转义的空格
  if (content.match(/(^|[^\\])(\\\\)*\s/)) {
    state.pos = start; // 恢复 pos
    return false;
  }

  // --- 匹配成功，生成 token ---

  // 记录结束位置，以便在生成 token 后恢复 state.pos
  const originalPos = state.pos;
  state.posMax = originalPos; // 将 posMax 设置为结束 '^' 的位置
  state.pos = start + 1; // 将 pos 设置到内容的开始位置

  // 创建 'sup_open' token
  const token_so: Token = state.push('sup_open', 'sup', 1);
  token_so.markup = '^';

  // 创建 'text' token，包含处理转义后的内容
  const token_t: Token = state.push('text', '', 0);
  token_t.content = content.replace(UNESCAPE_RE, '$1'); // 应用转义处理

  // 创建 'sup_close' token
  const token_sc: Token = state.push('sup_close', 'sup', -1);
  token_sc.markup = '^';

  // 恢复状态
  state.pos = originalPos + 1; // pos 跳过结束 '^'
  state.posMax = max; // 恢复原始的 posMax

  return true; // 匹配成功
}

// 定义插件函数，并指定参数类型
export function supPlugin(md: MarkdownIt): void {
  // 将 superscript 规则添加到内联规则中
  // 放在 'emphasis' (加粗/斜体) 规则之后，以便先匹配加粗/斜体
  md.inline.ruler.after('emphasis', 'sup', superscript);

  // 可选：添加上标 token 的渲染规则，如果默认渲染不符合要求
  // md.renderer.rules.sup_open = (tokens, idx, options, env, self) => '<sup>';
  // md.renderer.rules.sup_close = (tokens, idx, options, env, self) => '</sup>';
  // 注意：我们在前面的 Pipe 代码中已经添加了渲染规则，这里不再重复。
}
