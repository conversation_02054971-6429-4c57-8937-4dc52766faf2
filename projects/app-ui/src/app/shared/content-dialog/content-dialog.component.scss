.content-dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  width: 100%;
  max-width: 90vw;
  overflow: hidden;
  position: relative; /* 添加相对定位，使子元素可以正确定位 */
}

.content-dialog-header {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.content-dialog-body {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐，避免代码块在垂直方向居中 */
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.content-dialog-footer {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xs);
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.content-dialog-toolbar {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xs);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-sm);

  .toolbar-button {
    margin: 0 var(--spacing-xxs);
  }
}

// 代码内容样式
.code-content {
  width: 100%;
  max-width: 100%;
  overflow: auto;
  align-self: flex-start;
  background-color: var(--code-bg-color);
  border-radius: var(--border-radius-sm);
  box-sizing: border-box;
  margin-bottom: var(--spacing-md);

  pre {
    margin: 0;
    overflow: auto;
    padding: var(--spacing-md);
    max-height: calc(90vh - 150px); /* 限制高度，留出空间给工具栏 */
    code {
      padding: 0;
    }
  }
}

// 图片内容样式
.image-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

// 视频内容样式
.video-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  video {
    max-width: 100%;
    max-height: 100%;
  }
}

// 音频内容样式
.audio-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  audio {
    width: 100%;
    max-width: 600px;
  }
}

// Mermaid 和 PlantUML 内容样式
.mermaid-content,
.plantuml-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: auto;

  svg, img {
    max-width: 100%;
    max-height: 100%;
  }
}

// 默认内容样式
.default-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-word;
}
