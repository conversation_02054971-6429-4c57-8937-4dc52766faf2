<div class="content-dialog-container">
  <div class="content-dialog-header">
    <button mat-icon-button class="close-button" (click)="onClose()">
      <mat-icon svgIcon="close"></mat-icon>
    </button>
  </div>

  <div class="content-dialog-body">
    <!-- 根据内容类型显示不同的内容 -->
    @switch (data.type) {
      @case ('code') {
        <div class="content-wrapper">
          <div class="code-content">
            <pre><code [innerHTML]="data.content|safeHtml">无代码内容</code></pre>
          </div>
        </div>
      }
      @case ('image') {
        <div class="content-wrapper">
          <div class="image-content">
            <img [src]="data.additionalData?.imageUrl" alt="图片内容" />
          </div>
        </div>
      }
      @case ('video') {
        <div class="content-wrapper">
          <div class="video-content">
            <video controls [src]="data.additionalData?.videoUrl"></video>
          </div>
        </div>
      }
      @case ('audio') {
        <div class="content-wrapper">
          <div class="audio-content">
            <audio controls [src]="data.additionalData?.audioUrl"></audio>
          </div>
        </div>
      }
      @case ('mermaid') {
        <div class="content-wrapper">
          <div class="mermaid-content" [innerHTML]="data.additionalData?.renderedSvg | safeHtml"></div>
        </div>
      }
      @case ('plantuml') {
        <div class="content-wrapper">
          <div class="plantuml-content" [innerHTML]="data.additionalData?.renderedSvg | safeHtml"></div>
        </div>
      }
      @default {
        <div class="content-wrapper">
          <div class="default-content">
            {{ data.content }}
          </div>
        </div>
      }
    }
  </div>

  <!-- 底部工具栏 -->
  <div class="content-dialog-footer">
    <div class="content-dialog-toolbar">
      <!-- 代码工具栏 -->
      @if (data.type === 'code') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="下载"
          (click)="downloadCode()"
        >
          <mat-icon svgIcon="download"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制"
          (click)="copyCode()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }

      <!-- 图片工具栏 -->
      @if (data.type === 'image') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="下载"
          (click)="downloadImage()"
        >
          <mat-icon svgIcon="download"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="打印"
          (click)="printImage()"
        >
          <mat-icon svgIcon="print"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制"
          (click)="copyImage()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }

      <!-- 视频工具栏 -->
      @if (data.type === 'video') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="下载"
          (click)="downloadVideo()"
        >
          <mat-icon svgIcon="download"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制链接"
          (click)="copyVideoUrl()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }

      <!-- 音频工具栏 -->
      @if (data.type === 'audio') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="下载"
          (click)="downloadAudio()"
        >
          <mat-icon svgIcon="download"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制链接"
          (click)="copyAudioUrl()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }

      <!-- Mermaid 工具栏 -->
      @if (data.type === 'mermaid') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="导出 SVG"
          (click)="exportMermaidSvg()"
        >
          <mat-icon svgIcon="code"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="导出 PNG"
          (click)="exportMermaidPng()"
        >
          <mat-icon svgIcon="image"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制代码"
          (click)="copyMermaidCode()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }

      <!-- PlantUML 工具栏 -->
      @if (data.type === 'plantuml') {
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="导出 SVG"
          (click)="exportPlantumlSvg()"
        >
          <mat-icon svgIcon="code"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="导出 PNG"
          (click)="exportPlantumlPng()"
        >
          <mat-icon svgIcon="image"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="复制代码"
          (click)="copyPlantumlCode()"
        >
          <mat-icon svgIcon="copy"></mat-icon>
        </button>
        <button
          mat-icon-button
          class="toolbar-button"
          matTooltip="退出全屏"
          (click)="onClose()"
        >
          <mat-icon svgIcon="fullscreen_exit"></mat-icon>
        </button>
      }
    </div>
  </div>
</div>
