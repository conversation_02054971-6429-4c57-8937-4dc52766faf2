import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { SafeHtmlPipe } from '../pipes/safe-html.pipe';
import { delay } from '../utils/delay';

export interface ContentDialogData {
  code?: string;
  content: string;
  type: 'code' | 'image' | 'video' | 'audio' | 'mermaid' | 'plantuml';
  additionalData?: any;
}

@Component({
  selector: 'app-content-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSnackBarModule,
    SafeHtmlPipe,
  ],
  templateUrl: './content-dialog.component.html',
  styleUrl: './content-dialog.component.scss',
})
export class ContentDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ContentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ContentDialogData,
    private snackBar: MatSnackBar
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }

  // 代码相关操作
  copyCode(): void {
    if (this.data.content) {
      navigator.clipboard
        .writeText(this.data.code ?? '')
        .then(() => {
          this.snackBar.open('代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 下载代码文件
   */
  downloadCode(): void {
    if (this.data.code) {
      const blob = new Blob([this.data.code], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.getCodeFileName();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.snackBar.open('代码已下载', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 获取代码文件名
   */
  private getCodeFileName(): string {
    // 根据语言生成适当的文件扩展名
    let extension = '.txt';
    const language = this.data.additionalData?.language || '';

    switch (language) {
      case 'javascript':
        extension = '.js';
        break;
      case 'typescript':
        extension = '.ts';
        break;
      case 'html':
        extension = '.html';
        break;
      case 'css':
        extension = '.css';
        break;
      case 'scss':
        extension = '.scss';
        break;
      case 'python':
        extension = '.py';
        break;
      case 'java':
        extension = '.java';
        break;
      case 'c':
        extension = '.c';
        break;
      case 'cpp':
        extension = '.cpp';
        break;
      case 'csharp':
        extension = '.cs';
        break;
      case 'php':
        extension = '.php';
        break;
      case 'ruby':
        extension = '.rb';
        break;
      case 'go':
        extension = '.go';
        break;
      case 'rust':
        extension = '.rs';
        break;
      case 'swift':
        extension = '.swift';
        break;
      case 'kotlin':
        extension = '.kt';
        break;
      case 'sql':
        extension = '.sql';
        break;
      case 'xml':
        extension = '.xml';
        break;
      case 'json':
        extension = '.json';
        break;
      case 'yaml':
      case 'yml':
        extension = '.yaml';
        break;
      case 'markdown':
      case 'md':
        extension = '.md';
        break;
      case 'bash':
      case 'shell':
        extension = '.sh';
        break;
      case 'powershell':
        extension = '.ps1';
        break;
      case 'dockerfile':
        extension = '.dockerfile';
        break;
      default:
        extension = '.txt';
    }

    // 生成时间戳作为文件名的一部分
    const timestamp = new Date().getTime();
    return `code_${timestamp}${extension}`;
  }

  // 图片相关操作
  downloadImage(): void {
    const imageUrl = this.data.additionalData?.imageUrl;
    if (imageUrl) {
      const a = document.createElement('a');
      a.href = imageUrl;
      a.download = this.getFileName(imageUrl, 'image');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('图片下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  copyImage(): void {
    const imageUrl = this.data.additionalData?.imageUrl;
    if (imageUrl) {
      // 创建一个Canvas元素
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.crossOrigin = 'anonymous';
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        if (ctx) {
          ctx.drawImage(img, 0, 0);
          canvas.toBlob((blob) => {
            if (blob) {
              try {
                // 尝试使用现代API复制图片
                navigator.clipboard
                  .write([
                    new ClipboardItem({
                      [blob.type]: blob,
                    }),
                  ])
                  .then(() => {
                    this.snackBar.open('图片已复制到剪贴板', '关闭', {
                      duration: 2000,
                    });
                  })
                  .catch((err) => {
                    console.error('复制失败:', err);
                    this.snackBar.open('复制失败，请检查浏览器权限', '关闭', {
                      duration: 2000,
                    });
                  });
              } catch (e) {
                this.snackBar.open('您的浏览器不支持图片复制', '关闭', {
                  duration: 2000,
                });
              }
            }
          });
        }
      };

      img.onerror = () => {
        this.snackBar.open('无法加载图片，可能是跨域限制', '关闭', {
          duration: 2000,
        });
      };

      img.src = imageUrl;
    }
  }

  printImage(): void {
    const imageUrl = this.data.additionalData?.imageUrl;
    if (imageUrl) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>打印图片</title>
              <style>
                body {
                  margin: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
              </style>
            </head>
            <body>
              <img src="${imageUrl}" alt="打印图片">
              <script>
                window.onload = function() {
                  setTimeout(function() {
                    window.print();
                    window.close();
                  }, 500);
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    }
  }

  // 视频相关操作
  downloadVideo(): void {
    const videoUrl = this.data.additionalData?.videoUrl;
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = this.getFileName(videoUrl, 'video');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('视频下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  copyVideoUrl(): void {
    const videoUrl = this.data.additionalData?.videoUrl;
    if (videoUrl) {
      navigator.clipboard
        .writeText(videoUrl)
        .then(() => {
          this.snackBar.open('视频链接已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  // 音频相关操作
  downloadAudio(): void {
    const audioUrl = this.data.additionalData?.audioUrl;
    if (audioUrl) {
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = this.getFileName(audioUrl, 'audio');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('音频下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  copyAudioUrl(): void {
    const audioUrl = this.data.additionalData?.audioUrl;
    if (audioUrl) {
      navigator.clipboard
        .writeText(audioUrl)
        .then(() => {
          this.snackBar.open('音频链接已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  // Mermaid 相关操作
  copyMermaidCode(): void {
    if (this.data.content) {
      navigator.clipboard
        .writeText(decodeURIComponent(this.data.content))
        .then(() => {
          this.snackBar.open('Mermaid 代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  exportMermaidSvg(): void {
    const svgElement = document.querySelector('.mermaid-content svg');
    if (svgElement) {
      // 克隆 SVG 元素以避免修改原始元素
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;

      // 确保 SVG 有正确的命名空间
      if (!clonedSvg.getAttribute('xmlns')) {
        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }

      // 将 SVG 转换为字符串
      const svgData = new XMLSerializer().serializeToString(clonedSvg);

      // 创建 Blob 对象
      const blob = new Blob([svgData], { type: 'image/svg+xml' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mermaid_${new Date().getTime()}.svg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.snackBar.open('SVG 导出成功', '关闭', {
        duration: 2000,
      });
    } else {
      this.snackBar.open('未找到 SVG 元素', '关闭', {
        duration: 2000,
      });
    }
  }

  exportMermaidPng(): void {
    const svgElement = document.querySelector('.mermaid-content svg');
    if (svgElement) {
      // 克隆 SVG 元素以避免修改原始元素
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;

      // 确保 SVG 有正确的命名空间
      if (!clonedSvg.getAttribute('xmlns')) {
        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }

      // 获取 SVG 的尺寸
      const bbox = svgElement.getBoundingClientRect();
      const width = bbox.width;
      const height = bbox.height;

      // 将 SVG 转换为字符串
      const svgData = new XMLSerializer().serializeToString(clonedSvg);

      // 创建 Image 对象
      const img = new Image();
      img.onload = () => {
        // 创建 Canvas 元素
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        // 获取 Canvas 上下文
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // 绘制图像
          ctx.drawImage(img, 0, 0, width, height);

          // 导出为 PNG
          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `mermaid_${new Date().getTime()}.png`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);

              this.snackBar.open('PNG 导出成功', '关闭', {
                duration: 2000,
              });
            }
          }, 'image/png');
        }
      };

      // 设置 Image 的 src
      img.src =
        'data:image/svg+xml;base64,' +
        btoa(unescape(encodeURIComponent(svgData)));
    } else {
      this.snackBar.open('未找到 SVG 元素', '关闭', {
        duration: 2000,
      });
    }
  }

  // PlantUML 相关操作
  copyPlantumlCode(): void {
    if (this.data.content) {
      navigator.clipboard
        .writeText(this.data.content)
        .then(() => {
          this.snackBar.open('PlantUML 代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  exportPlantumlSvg(): void {
    const imgElement = document.querySelector<HTMLImageElement>(
      '.plantuml-content img.plantuml-image'
    );
    if (imgElement && imgElement.src) {
      // 使用 fetch 获取 SVG 内容，然后创建 Blob 对象下载
      fetch(imgElement.src)
        .then((response) => response.blob())
        .then((blob) => {
          // 创建 Blob URL
          const url = URL.createObjectURL(blob);

          // 创建下载链接
          const a = document.createElement('a');
          a.href = url;
          a.download = `plantuml_${new Date().getTime()}.svg`;
          a.style.display = 'none';
          document.body.appendChild(a);

          // 触发下载
          a.click();

          // 清理
          this.cleanupDownload(a, url);

          this.snackBar.open('SVG 导出成功', '关闭', {
            duration: 2000,
          });
        })
        .catch((error) => {
          console.error('导出 SVG 失败:', error);
          this.snackBar.open('导出失败', '关闭', {
            duration: 2000,
          });
        });
    } else {
      this.snackBar.open('未找到图像元素', '关闭', {
        duration: 2000,
      });
    }
  }

  exportPlantumlPng(): void {
    const imgElement = document.querySelector<HTMLImageElement>(
      '.plantuml-content img.plantuml-image'
    );
    if (imgElement && imgElement.src) {
      // 将 SVG URL 转换为 PNG URL
      const svgUrl = imgElement.src;
      const pngUrl = svgUrl.replace('/svg/', '/png/');

      // 使用 fetch 获取 PNG 内容，然后创建 Blob 对象下载
      fetch(pngUrl)
        .then((response) => response.blob())
        .then((blob) => {
          // 创建 Blob URL
          const url = URL.createObjectURL(blob);

          // 创建下载链接
          const a = document.createElement('a');
          a.href = url;
          a.download = `plantuml_${new Date().getTime()}.png`;
          a.style.display = 'none';
          document.body.appendChild(a);

          // 触发下载
          a.click();

          // 清理
          this.cleanupDownload(a, url);

          this.snackBar.open('PNG 导出成功', '关闭', {
            duration: 2000,
          });
        })
        .catch((error) => {
          console.error('导出 PNG 失败:', error);
          this.snackBar.open('导出失败', '关闭', {
            duration: 2000,
          });
        });
    } else {
      this.snackBar.open('未找到图像元素', '关闭', {
        duration: 2000,
      });
    }
  }

  // 通用方法
  private getFileName(url: string, defaultType: string): string {
    if (url) {
      const urlParts = url.split('/');
      let fileName = urlParts[urlParts.length - 1];

      // 移除URL参数
      fileName = fileName.split('?')[0];

      if (fileName) {
        return fileName;
      }
    }

    // 默认文件名
    const timestamp = new Date().getTime();
    return `${defaultType}_${timestamp}.${this.getDefaultExtension(
      defaultType
    )}`;
  }

  private getDefaultExtension(type: string): string {
    switch (type) {
      case 'image':
        return 'png';
      case 'video':
        return 'mp4';
      case 'audio':
        return 'mp3';
      default:
        return 'txt';
    }
  }

  /**
   * 清理下载元素
   * @param element 要移除的DOM元素
   * @param url 要释放的URL
   */
  private async cleanupDownload(
    element: HTMLElement,
    url: string
  ): Promise<void> {
    await delay(100);
    document.body.removeChild(element);
    URL.revokeObjectURL(url);
  }
}
