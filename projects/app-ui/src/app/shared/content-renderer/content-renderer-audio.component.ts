import { Component, ElementRef, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';

/**
 * 音频包装器组件
 * 提供音频特有的功能，如下载、复制链接等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-audio',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
  ],
  templateUrl: './content-renderer-audio.component.html',
  styleUrls: ['./content-renderer-audio.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererAudioComponent implements OnInit {
  @Input() audioUrl = '';
  @Input() title = '';

  isFullscreen = false;
  audioElement: HTMLAudioElement | null = null;

  constructor(
    private el: ElementRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // 如果没有提供音频URL，尝试从内部获取
    if (!this.audioUrl) {
      this.audioElement = this.el.nativeElement.querySelector('audio');
      if (this.audioElement) {
        this.audioUrl = this.audioElement.src || '';
        this.title = this.audioElement.title || '';
      }
    }
  }

  /**
   * 下载音频
   */
  downloadAudio(): void {
    if (this.audioUrl) {
      const a = document.createElement('a');
      a.href = this.audioUrl;
      a.download = this.getFileName();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('音频下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 复制音频URL到剪贴板
   */
  copyAudioUrl(): void {
    if (this.audioUrl) {
      navigator.clipboard
        .writeText(this.audioUrl)
        .then(() => {
          this.snackBar.open('音频链接已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 在对话框中打开音频
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          content: '',
          type: 'audio',
          additionalData: {
            audioUrl: this.audioUrl,
          },
        },
      }
    );
  }

  /**
   * 获取文件名
   */
  private getFileName(): string {
    if (this.audioUrl) {
      const urlParts = this.audioUrl.split('/');
      let fileName = urlParts[urlParts.length - 1];

      // 移除URL参数
      fileName = fileName.split('?')[0];

      if (fileName) {
        return fileName;
      }
    }

    // 默认文件名
    const timestamp = new Date().getTime();
    return `audio_${timestamp}.mp3`;
  }
}
