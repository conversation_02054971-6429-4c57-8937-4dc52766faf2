<div class="content-renderer-plantuml" [class.fullscreen]="isFullscreen">
  <div class="plantuml-container">
    <!-- 加载中状态 -->
    @if (loading) {
      <div class="plantuml-loading">
        正在加载 PlantUML 图表...
      </div>
    }

    <!-- 错误状态 -->
    @if (error) {
      <div class="plantuml-error">
        <div class="error-title">PlantUML 错误</div>
        <div class="error-message">{{ error }}</div>
      </div>
    }

    <!-- 渲染结果 -->
    @if (!loading && !error && renderedSvg) {
      <div [innerHTML]="renderedSvg | safeHtml"></div>
    }
  </div>
  <div class="toolbar">
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="导出 SVG"
      (click)="exportSvg()"
    >
      <mat-icon svgIcon="code"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="导出 PNG"
      (click)="exportPng()"
    >
      <mat-icon svgIcon="image"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="复制代码"
      (click)="copyPlantumlCode()"
    >
      <mat-icon svgIcon="copy"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="全屏"
      (click)="toggleFullscreen()"
    >
      <mat-icon svgIcon="fullscreen"></mat-icon>
    </button>
  </div>
</div>
