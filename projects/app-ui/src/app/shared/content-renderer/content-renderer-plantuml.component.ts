import { Component, effect, ElementRef, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import * as plantumlEncoder from 'plantuml-encoder';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';
import { SafeHtmlPipe } from '../pipes/safe-html.pipe';

/**
 * PlantUML 图表包装器组件
 * 提供 PlantUML 图表特有的功能，如导出 SVG、PNG 等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-plantuml',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
    SafeHtmlPipe,
  ],
  templateUrl: './content-renderer-plantuml.component.html',
  styleUrls: ['./content-renderer-plantuml.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererPlantumlComponent {
  @Input() code = '';

  isFullscreen = false;

  // 普通属性用于存储状态
  renderedSvg = '';
  loading = true;
  error = '';

  constructor(
    private el: ElementRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    // 初始化加载状态
    this.loading = true;
    this.error = '';

    // 创建一个 effect 来响应 code 的变化
    effect(() => {
      this.renderPlantUML(this.code);
    });
  }

  /**
   * 渲染 PlantUML 图表
   */
  private renderPlantUML(code: string): void {
    // 重置状态
    this.loading = true;
    this.error = '';
    this.renderedSvg = '';

    if (!code) {
      this.loading = false;
      this.error = '没有提供 PlantUML 内容';
      return;
    }

    try {
      // 使用 PlantUML 编码算法将内容转换为编码字符串
      const encodedContent = plantumlEncoder.encode(decodeURIComponent(code));

      // 构建 PlantUML 服务器 URL (使用公共服务器)
      // 指定输出格式为 SVG
      const plantUmlServerUrl = 'https://www.plantuml.com/plantuml/svg/';
      const imageUrl = plantUmlServerUrl + encodedContent;

      // 创建图像元素，使用 img 标签加载 SVG
      const svgContainer = `
          <div class="plantuml-container">
            <img src="${imageUrl}" alt="PlantUML Diagram" class="plantuml-image" />
          </div>
        `;

      // 添加图像加载事件监听
      const img = new Image();
      img.onload = () => {
        this.loading = false;
        this.renderedSvg = svgContainer;
      };
      img.onerror = (err) => {
        console.error('PlantUML 图表加载失败:', err);
        this.loading = false;
        this.error = '图表加载失败，请检查网络或 PlantUML 语法';
      };
      img.src = imageUrl;
    } catch (error) {
      console.error('PlantUML 渲染错误:', error);
      this.loading = false;
      this.error = `渲染失败: ${error}`;
    }
  }

  /**
   * 导出 SVG
   */
  exportSvg(): void {
    const imgElement =
      this.el.nativeElement.querySelector('img.plantuml-image');
    if (imgElement && imgElement.src) {
      // 使用 fetch 获取 SVG 内容，然后创建 Blob 对象下载
      fetch(imgElement.src)
        .then((response) => response.blob())
        .then((blob) => {
          // 创建 Blob URL
          const url = URL.createObjectURL(blob);

          // 创建下载链接
          const a = document.createElement('a');
          a.href = url;
          a.download = `plantuml_${new Date().getTime()}.svg`;
          a.style.display = 'none';
          document.body.appendChild(a);

          // 触发下载
          a.click();

          // 清理
          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }, 100);

          this.snackBar.open('SVG 导出成功', '关闭', {
            duration: 2000,
          });
        })
        .catch((error) => {
          console.error('导出 SVG 失败:', error);
          this.snackBar.open('导出失败', '关闭', {
            duration: 2000,
          });
        });

      // 成功消息已经在 fetch 的 then 中显示
    } else {
      this.snackBar.open('未找到图像元素', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 导出 PNG
   */
  exportPng(): void {
    const imgElement =
      this.el.nativeElement.querySelector('img.plantuml-image');
    if (imgElement && imgElement.src) {
      // 将 SVG URL 转换为 PNG URL
      const svgUrl = imgElement.src;
      const pngUrl = svgUrl.replace('/svg/', '/png/');

      // 使用 fetch 获取 PNG 内容，然后创建 Blob 对象下载
      fetch(pngUrl)
        .then((response) => response.blob())
        .then((blob) => {
          // 创建 Blob URL
          const url = URL.createObjectURL(blob);

          // 创建下载链接
          const a = document.createElement('a');
          a.href = url;
          a.download = `plantuml_${new Date().getTime()}.png`;
          a.style.display = 'none';
          document.body.appendChild(a);

          // 触发下载
          a.click();

          // 清理
          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }, 100);

          this.snackBar.open('PNG 导出成功', '关闭', {
            duration: 2000,
          });
        })
        .catch((error) => {
          console.error('导出 PNG 失败:', error);
          this.snackBar.open('导出失败', '关闭', {
            duration: 2000,
          });
        });

      // 成功消息已经在 fetch 的 then 中显示
    } else {
      this.snackBar.open('未找到图像元素', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 复制 PlantUML 代码到剪贴板
   */
  copyPlantumlCode(): void {
    if (this.code) {
      navigator.clipboard
        .writeText(this.code)
        .then(() => {
          this.snackBar.open('PlantUML 代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 在对话框中打开 PlantUML 图表
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          content: this.code,
          type: 'plantuml',
          additionalData: {
            renderedSvg: this.renderedSvg,
          },
        },
      }
    );
  }
}
