.content-renderer-plantuml {
  .plantuml-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-xs);
    overflow: auto;
    min-height: 100px;
  }

  .plantuml-loading {
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);
    padding: var(--spacing-md);
    text-align: center;
  }

  .plantuml-error {
    color: var(--error-color);
    background-color: var(--error-bg-color, rgba(244, 67, 54, 0.1));
    border: 1px solid var(--error-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    width: 100%;
    max-width: 600px;

    .error-title {
      font-weight: bold;
      margin-bottom: var(--spacing-xs);
    }

    .error-message {
      font-family: var(--font-family-code);
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .plantuml-image {
    max-width: 100%;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
  }

  &.fullscreen {
    .plantuml-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        max-width: 100%;
        max-height: 90vh;
      }
    }
  }
}
