.content-renderer-audio {
  .audio-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-xs);
  }

  audio {
    width: 100%;
    border-radius: var(--border-radius-sm);
  }

  &.fullscreen {
    .audio-container {
      max-width: 90%;
      width: 600px;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      audio {
        width: 100%;
      }
    }
  }
}
