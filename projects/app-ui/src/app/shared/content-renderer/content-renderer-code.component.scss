.content-renderer-code {
  .code-container {
    position: relative;
    width: 100%;
    overflow-x: auto;
  }

  .code-language {
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--spacing-xxs) var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    background-color: var(--secondary-color);
    border-bottom-left-radius: var(--border-radius-sm);
    z-index: 1;
  }

  pre {
    margin: 0;
    padding-top: var(--spacing-lg) !important;
  }

  &.fullscreen {
    .code-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
    }
  }
}
