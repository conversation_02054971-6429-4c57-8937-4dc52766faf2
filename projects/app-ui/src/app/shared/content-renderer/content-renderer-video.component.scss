.content-renderer-video {
  .video-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-xs);
  }

  video {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
  }

  &.fullscreen {
    .video-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      video {
        max-width: 100%;
        max-height: 90vh;
      }
    }
  }
}
