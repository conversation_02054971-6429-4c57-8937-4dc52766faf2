<div class="content-renderer-mermaid" [class.fullscreen]="isFullscreen">
  <div class="mermaid-container">
    <!-- 加载中状态 -->
    @if (loading) {
      <div class="mermaid-loading">
        正在加载 Mermaid 图表...
      </div>
    }

    <!-- 错误状态 -->
    @if (error) {
      <div class="mermaid-error">
        <div class="error-title">Mermaid 错误</div>
        <div class="error-message">{{ error }}</div>
      </div>
    }

    <!-- 渲染结果 -->
    @if (!loading && !error && renderedSvg) {
      <div [innerHTML]="renderedSvg | safeHtml"></div>
    }
  </div>
  <div class="toolbar">
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="导出 SVG"
      (click)="exportSvg()"
    >
      <mat-icon svgIcon="code"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="导出 PNG"
      (click)="exportPng()"
    >
      <mat-icon svgIcon="image"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="复制代码"
      (click)="copyMermaidCode()"
    >
      <mat-icon svgIcon="copy"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="全屏"
      (click)="toggleFullscreen()"
    >
      <mat-icon svgIcon="fullscreen"></mat-icon>
    </button>
  </div>
</div>
