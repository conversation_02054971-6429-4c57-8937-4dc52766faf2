<div class="content-renderer-code" [class.fullscreen]="isFullscreen">
  <div class="code-container">
    <pre [attr.data-language]="detectedLang"><code class="hljs language-{{detectedLang}}"
                                                   [innerHTML]="highlightedCode | safeHtml"></code></pre>
  </div>
  <div class="toolbar">
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="下载"
      (click)="downloadCode()"
    >
      <mat-icon svgIcon="download"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="复制"
      (click)="copyCode()"
    >
      <mat-icon svgIcon="copy"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="全屏"
      (click)="toggleFullscreen()"
    >
      <mat-icon svgIcon="fullscreen"></mat-icon>
    </button>
  </div>
</div>
