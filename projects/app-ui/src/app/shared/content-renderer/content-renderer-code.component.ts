import { Component, effect, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';
import { autoDetectLanguageSubset } from '../pipes/plugins/auto-detect-language-subset';
import { SafeHtmlPipe } from '../pipes/safe-html.pipe';

/**
 * 代码块包装器组件
 * 提供代码块特有的功能，如复制、运行等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-code',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
    SafeHtmlPipe,
  ],
  templateUrl: './content-renderer-code.component.html',
  styleUrls: ['./content-renderer-code.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererCodeComponent {
  @Input() language = '';
  @Input() code = '';
  decodedCode = '';
  highlightedCode = '';
  detectedLang = '';

  isFullscreen = false;

  constructor(private snackBar: MatSnackBar, private dialog: MatDialog) {
    effect(() => this.update());
  }

  async update() {
    const hljs = (await import('highlight.js')).default;
    const code = (this.decodedCode = decodeURIComponent(this.code));
    // 尝试进行代码高亮
    try {
      // 如果指定了语言
      if (this.language && hljs.listLanguages().includes(this.language)) {
        // 使用指定的语言进行高亮
        const result = hljs.highlight(code, {
          language: this.language,
          ignoreIllegals: true,
        });
        this.highlightedCode = result.value;
        this.detectedLang = result.language || this.language;
      } else {
        // 自动检测语言，使用我们定义的语言子集
        const result = hljs.highlightAuto(code, autoDetectLanguageSubset);
        this.highlightedCode = result.value;
        this.detectedLang = result.language || this.language;
      }
    } catch (e) {
      console.warn('代码高亮失败:', e);
      // 失败时回退到原始内容
      this.highlightedCode = code;
      this.detectedLang = this.language;
    }
  }

  /**
   * 复制代码到剪贴板
   */
  copyCode(): void {
    if (this.code) {
      navigator.clipboard
        .writeText(this.decodedCode)
        .then(() => {
          this.snackBar.open('代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 下载代码文件
   */
  downloadCode(): void {
    if (this.decodedCode) {
      const blob = new Blob([this.decodedCode], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.getFileName();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.snackBar.open('代码已下载', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 在对话框中打开代码
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          code: this.decodedCode,
          content: this.highlightedCode,
          type: 'code',
          additionalData: {
            language: this.language,
          },
        },
      }
    );
  }

  /**
   * 获取文件名
   */
  private getFileName(): string {
    // 根据语言生成适当的文件扩展名
    let extension = '.txt';

    switch (this.detectedLang) {
      case 'javascript':
        extension = '.js';
        break;
      case 'typescript':
        extension = '.ts';
        break;
      case 'html':
        extension = '.html';
        break;
      case 'css':
        extension = '.css';
        break;
      case 'scss':
        extension = '.scss';
        break;
      case 'python':
        extension = '.py';
        break;
      case 'java':
        extension = '.java';
        break;
      case 'c':
        extension = '.c';
        break;
      case 'cpp':
        extension = '.cpp';
        break;
      case 'csharp':
        extension = '.cs';
        break;
      case 'php':
        extension = '.php';
        break;
      case 'ruby':
        extension = '.rb';
        break;
      case 'go':
        extension = '.go';
        break;
      case 'rust':
        extension = '.rs';
        break;
      case 'swift':
        extension = '.swift';
        break;
      case 'kotlin':
        extension = '.kt';
        break;
      case 'sql':
        extension = '.sql';
        break;
      case 'xml':
        extension = '.xml';
        break;
      case 'json':
        extension = '.json';
        break;
      case 'yaml':
      case 'yml':
        extension = '.yaml';
        break;
      case 'markdown':
      case 'md':
        extension = '.md';
        break;
      case 'bash':
      case 'shell':
        extension = '.sh';
        break;
      case 'powershell':
        extension = '.ps1';
        break;
      case 'dockerfile':
        extension = '.dockerfile';
        break;
      default:
        extension = '.txt';
    }

    // 生成时间戳作为文件名的一部分
    const timestamp = new Date().getTime();
    return `code_${timestamp}${extension}`;
  }
}
