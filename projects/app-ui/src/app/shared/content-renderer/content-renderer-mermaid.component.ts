import { Component, effect, ElementRef, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';
import { SafeHtmlPipe } from '../pipes/safe-html.pipe';

/**
 * Mermaid 图表包装器组件
 * 提供 Mermaid 图表特有的功能，如导出 SVG、PNG 等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-mermaid',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
    SafeHtmlPipe,
  ],
  templateUrl: './content-renderer-mermaid.component.html',
  styleUrls: ['./content-renderer-mermaid.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererMermaidComponent {
  @Input() code = '';

  isFullscreen = false;

  private static idCounter = 0;
  private readonly diagramId: string;

  // 普通属性用于存储渲染结果
  renderedSvg = '';

  // 普通属性用于存储状态
  loading = true;
  error = '';

  constructor(
    private el: ElementRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.diagramId = `mermaid-diagram-${ContentRendererMermaidComponent.idCounter++}`;

    // 初始化加载状态
    this.loading = true;
    this.error = '';

    // 创建一个 effect 来响应 code 的变化
    effect(() => {
      this.renderMermaid(this.code);
    });
  }

  // 注意：renderedSvg 现在是一个普通属性，不再是计算属性

  // 渲染 Mermaid 图表
  private renderMermaid(code: string): void {
    // 重置状态
    this.loading = true;
    this.error = '';
    this.renderedSvg = '';

    if (!code) {
      this.loading = false;
      this.error = '没有提供 Mermaid 内容';
      return;
    }

    try {
      // 动态导入 mermaid
      import('mermaid')
        .then((module) => {
          const mermaid = module.default;

          // 初始化 mermaid
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
          });

          // 渲染图表
          mermaid
            .render(this.diagramId, decodeURIComponent(code))
            .then(({ svg }) => {
              // 更新信号值
              this.renderedSvg = svg;
              this.loading = false;
            })
            .catch((error) => {
              console.error('Mermaid 渲染错误:', error);
              this.loading = false;
              this.error = `图表渲染失败: ${error}`;
            });
        })
        .catch((error) => {
          console.error('加载 Mermaid 库失败:', error);
          this.loading = false;
          this.error = `加载 Mermaid 库失败: ${error}`;
        });
    } catch (error) {
      console.error('Mermaid 组件初始化错误:', error);
      this.loading = false;
      this.error = `初始化错误: ${error}`;
    }
  }

  /**
   * 导出 SVG
   */
  exportSvg(): void {
    const svgElement = this.el.nativeElement.querySelector('svg');
    if (svgElement) {
      // 克隆 SVG 元素以避免修改原始元素
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;

      // 确保 SVG 有正确的命名空间
      if (!clonedSvg.getAttribute('xmlns')) {
        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }

      // 将 SVG 转换为字符串
      const svgData = new XMLSerializer().serializeToString(clonedSvg);

      // 创建 Blob 对象
      const blob = new Blob([svgData], { type: 'image/svg+xml' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mermaid_${new Date().getTime()}.svg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.snackBar.open('SVG 导出成功', '关闭', {
        duration: 2000,
      });
    } else {
      this.snackBar.open('未找到 SVG 元素', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 导出 PNG
   */
  exportPng(): void {
    const svgElement = this.el.nativeElement.querySelector('svg');
    if (svgElement) {
      // 克隆 SVG 元素以避免修改原始元素
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;

      // 确保 SVG 有正确的命名空间
      if (!clonedSvg.getAttribute('xmlns')) {
        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }

      // 获取 SVG 的尺寸
      const bbox = svgElement.getBoundingClientRect();
      const width = bbox.width;
      const height = bbox.height;

      // 将 SVG 转换为字符串
      const svgData = new XMLSerializer().serializeToString(clonedSvg);

      // 创建 Image 对象
      const img = new Image();
      img.onload = () => {
        // 创建 Canvas 元素
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        // 获取 Canvas 上下文
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // 绘制图像
          ctx.drawImage(img, 0, 0, width, height);

          // 导出为 PNG
          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `mermaid_${new Date().getTime()}.png`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);

              this.snackBar.open('PNG 导出成功', '关闭', {
                duration: 2000,
              });
            }
          }, 'image/png');
        }
      };

      // 设置 Image 的 src
      img.src =
        'data:image/svg+xml;base64,' +
        btoa(unescape(encodeURIComponent(svgData)));
    } else {
      this.snackBar.open('未找到 SVG 元素', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 复制 Mermaid 代码到剪贴板
   */
  copyMermaidCode(): void {
    if (this.code) {
      navigator.clipboard
        .writeText(decodeURIComponent(this.code))
        .then(() => {
          this.snackBar.open('Mermaid 代码已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 在对话框中打开 Mermaid 图表
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          content: this.code,
          type: 'mermaid',
          additionalData: {
            renderedSvg: this.renderedSvg,
          },
        },
      }
    );
  }
}
