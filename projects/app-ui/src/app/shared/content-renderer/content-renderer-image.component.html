<div class="content-renderer-image" [class.fullscreen]="isFullscreen">
  <div class="image-container">
    <ng-content></ng-content>
  </div>
  <div class="toolbar">
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="下载"
      (click)="downloadImage()"
    >
      <mat-icon svgIcon="download"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="打印"
      (click)="printImage()"
    >
      <mat-icon svgIcon="print"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="复制"
      (click)="copyImage()"
    >
      <mat-icon svgIcon="copy"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="全屏"
      (click)="toggleFullscreen()"
    >
      <mat-icon svgIcon="fullscreen"></mat-icon>
    </button>
  </div>
</div>
