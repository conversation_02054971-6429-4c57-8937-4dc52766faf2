import { Component, ElementRef, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';

/**
 * 视频包装器组件
 * 提供视频特有的功能，如下载、复制链接等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-video',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
  ],
  templateUrl: './content-renderer-video.component.html',
  styleUrls: ['./content-renderer-video.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererVideoComponent implements OnInit {
  @Input() videoUrl = '';
  @Input() title = '';

  isFullscreen = false;
  videoElement: HTMLVideoElement | null = null;

  constructor(
    private el: ElementRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // 如果没有提供视频URL，尝试从内部获取
    if (!this.videoUrl) {
      this.videoElement = this.el.nativeElement.querySelector('video');
      if (this.videoElement) {
        this.videoUrl = this.videoElement.src || '';
        this.title = this.videoElement.title || '';
      }
    }
  }

  /**
   * 下载视频
   */
  downloadVideo(): void {
    if (this.videoUrl) {
      const a = document.createElement('a');
      a.href = this.videoUrl;
      a.download = this.getFileName();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('视频下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 复制视频URL到剪贴板
   */
  copyVideoUrl(): void {
    if (this.videoUrl) {
      navigator.clipboard
        .writeText(this.videoUrl)
        .then(() => {
          this.snackBar.open('视频链接已复制到剪贴板', '关闭', {
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error('复制失败:', err);
          this.snackBar.open('复制失败', '关闭', {
            duration: 2000,
          });
        });
    }
  }

  /**
   * 在对话框中打开视频
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          content: '',
          type: 'video',
          additionalData: {
            videoUrl: this.videoUrl,
          },
        },
      }
    );
  }

  /**
   * 获取文件名
   */
  private getFileName(): string {
    if (this.videoUrl) {
      const urlParts = this.videoUrl.split('/');
      let fileName = urlParts[urlParts.length - 1];

      // 移除URL参数
      fileName = fileName.split('?')[0];

      if (fileName) {
        return fileName;
      }
    }

    // 默认文件名
    const timestamp = new Date().getTime();
    return `video_${timestamp}.mp4`;
  }
}
