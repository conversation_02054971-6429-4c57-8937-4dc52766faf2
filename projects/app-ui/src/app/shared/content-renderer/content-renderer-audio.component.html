<div class="content-renderer-audio" [class.fullscreen]="isFullscreen">
  <div class="audio-container">
    <ng-content></ng-content>
  </div>
  <div class="toolbar">
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="下载"
      (click)="downloadAudio()"
    >
      <mat-icon svgIcon="download"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="复制链接"
      (click)="copyAudioUrl()"
    >
      <mat-icon svgIcon="copy"></mat-icon>
    </button>
    <button
      mat-icon-button
      class="toolbar-button"
      matTooltip="全屏"
      (click)="toggleFullscreen()"
    >
      <mat-icon svgIcon="fullscreen"></mat-icon>
    </button>
  </div>
</div>
