import { Component, ElementRef, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ContentDialogComponent, ContentDialogData } from '../content-dialog/content-dialog.component';

/**
 * 图片包装器组件
 * 提供图片特有的功能，如下载、打印等
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'content-renderer-image',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule,
  ],
  templateUrl: './content-renderer-image.component.html',
  styleUrls: ['./content-renderer-image.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContentRendererImageComponent implements OnInit {
  @Input() imageUrl = '';
  @Input() altText = '';

  isFullscreen = false;
  imageElement: HTMLImageElement | null = null;

  constructor(
    private el: ElementRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // 如果没有提供图片URL，尝试从内部获取
    if (!this.imageUrl) {
      this.imageElement = this.el.nativeElement.querySelector('img');
      if (this.imageElement) {
        this.imageUrl = this.imageElement.src || '';
        this.altText = this.imageElement.alt || '';
      }
    }
  }

  /**
   * 下载图片
   */
  downloadImage(): void {
    if (this.imageUrl) {
      const a = document.createElement('a');
      a.href = this.imageUrl;
      a.download = this.getFileName();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      this.snackBar.open('图片下载已开始', '关闭', {
        duration: 2000,
      });
    }
  }

  /**
   * 打印图片
   */
  printImage(): void {
    if (this.imageUrl) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>打印图片</title>
              <style>
                body {
                  margin: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
              </style>
            </head>
            <body>
              <img src="${this.imageUrl}" alt="${this.altText || '打印图片'}">
              <script>
                window.onload = function() {
                  setTimeout(function() {
                    window.print();
                    window.close();
                  }, 500);
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    }
  }

  /**
   * 复制图片到剪贴板
   */
  copyImage(): void {
    if (this.imageUrl) {
      // 创建一个Canvas元素
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.crossOrigin = 'anonymous';
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        if (ctx) {
          ctx.drawImage(img, 0, 0);
          canvas.toBlob((blob) => {
            if (blob) {
              try {
                // 尝试使用现代API复制图片
                navigator.clipboard
                  .write([
                    new ClipboardItem({
                      [blob.type]: blob,
                    }),
                  ])
                  .then(() => {
                    this.snackBar.open('图片已复制到剪贴板', '关闭', {
                      duration: 2000,
                    });
                  })
                  .catch((err) => {
                    console.error('复制失败:', err);
                    this.snackBar.open('复制失败，请检查浏览器权限', '关闭', {
                      duration: 2000,
                    });
                  });
              } catch (e) {
                this.snackBar.open('您的浏览器不支持图片复制', '关闭', {
                  duration: 2000,
                });
              }
            }
          });
        }
      };

      img.onerror = () => {
        this.snackBar.open('无法加载图片，可能是跨域限制', '关闭', {
          duration: 2000,
        });
      };

      img.src = this.imageUrl;
    }
  }

  /**
   * 在对话框中打开图片
   */
  toggleFullscreen(): void {
    this.dialog.open<ContentDialogComponent, ContentDialogData>(
      ContentDialogComponent,
      {
        width: '80vw',
        height: '80vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        panelClass: 'content-dialog',
        data: {
          content: '',
          type: 'image',
          additionalData: {
            imageUrl: this.imageUrl,
            altText: this.altText,
          },
        },
      }
    );
  }

  /**
   * 获取文件名
   */
  private getFileName(): string {
    if (this.imageUrl) {
      const urlParts = this.imageUrl.split('/');
      let fileName = urlParts[urlParts.length - 1];

      // 移除URL参数
      fileName = fileName.split('?')[0];

      if (fileName) {
        return fileName;
      }
    }

    // 默认文件名
    const timestamp = new Date().getTime();
    return `image_${timestamp}.png`;
  }
}
