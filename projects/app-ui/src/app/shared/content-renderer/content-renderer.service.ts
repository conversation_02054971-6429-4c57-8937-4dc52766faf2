import {
  Inject,
  Injectable,
  Injector,
  NgZone,
  PLATFORM_ID,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { createCustomElement } from '@angular/elements';
import { ContentRendererCodeComponent } from './content-renderer-code.component';
import { ContentRendererMermaidComponent } from './content-renderer-mermaid.component';
import { ContentRendererPlantumlComponent } from './content-renderer-plantuml.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ContentRendererVideoComponent } from './content-renderer-video.component';
import { ContentRendererAudioComponent } from './content-renderer-audio.component';
import { ContentRendererImageComponent } from './content-renderer-image.component';

@Injectable({
  providedIn: 'root',
})
export class ContentRendererService {
  private isBrowser: boolean;

  constructor(
    private injector: Injector,
    @Inject(PLATFORM_ID) platformId: object,
    private ngZone: NgZone,
    private snackBar: MatSnackBar
  ) {
    this.isBrowser = isPlatformBrowser(platformId);
  }

  /**
   * 注册所有包装器自定义元素
   */
  registerCustomElements(): void {
    // 只在浏览器环境中注册自定义元素
    if (!this.isBrowser) {
      return;
    }

    try {
      // 注册代码块包装器
      if (!customElements.get('content-renderer-code')) {
        const codeElement = createCustomElement(ContentRendererCodeComponent, {
          injector: this.injector,
        });
        customElements.define('content-renderer-code', codeElement);
      }

      // 注册媒体包装器
      if (!customElements.get('content-renderer-image')) {
        const imageElement = createCustomElement(
          ContentRendererImageComponent,
          {
            injector: this.injector,
          }
        );
        customElements.define('content-renderer-image', imageElement);
      }
      if (!customElements.get('content-renderer-audio')) {
        const audioElement = createCustomElement(
          ContentRendererAudioComponent,
          {
            injector: this.injector,
          }
        );
        customElements.define('content-renderer-audio', audioElement);
      }
      if (!customElements.get('content-renderer-video')) {
        const videoElement = createCustomElement(
          ContentRendererVideoComponent,
          {
            injector: this.injector,
          }
        );
        customElements.define('content-renderer-video', videoElement);
      }

      // 注册 Mermaid 包装器
      if (!customElements.get('content-renderer-mermaid')) {
        const mermaidElement = createCustomElement(
          ContentRendererMermaidComponent,
          {
            injector: this.injector,
          }
        );
        customElements.define('content-renderer-mermaid', mermaidElement);
      }

      // 注册 PlantUML 包装器
      if (!customElements.get('content-renderer-plantuml')) {
        const plantumlElement = createCustomElement(
          ContentRendererPlantumlComponent,
          {
            injector: this.injector,
          }
        );
        customElements.define('content-renderer-plantuml', plantumlElement);
      }

      // 所有包装器自定义元素已注册
    } catch (error) {
      console.error('注册包装器自定义元素失败:', error);
      this.snackBar.open('注册包装器自定义元素失败', '关闭', {
        duration: 3000,
      });
    }
  }
}
