import {HttpHandlerFn, HttpInterceptorFn, HttpRequest} from '@angular/common/http';
import {inject} from '@angular/core';
import {TokenHolder} from '../services/token-holder.service';

/**
 * 认证拦截器
 * 为API请求添加认证令牌
 */
export const authInterceptor: HttpInterceptorFn = (
    request: HttpRequest<unknown>,
    next: HttpHandlerFn,
) => {
  const tokenService = inject(TokenHolder);
  const token = tokenService.getToken();

  // 如果有令牌，则为请求添加Authorization头
  if (token) {
    const authRequest = request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
    return next(authRequest);
  }

  // 如果没有令牌，则不修改请求
  return next(request);
};
