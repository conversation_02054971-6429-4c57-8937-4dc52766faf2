import {HttpErrorResponse, HttpEvent, HttpHandlerFn, HttpInterceptorFn, HttpRequest} from '@angular/common/http';
import {inject, Injector} from '@angular/core';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {catchError, delay, Observable, of, switchMap, throwError} from 'rxjs';
import {LoginDialogComponent} from '../../auth/login-dialog/login-dialog.component';
import {AuthService} from '../services/auth.service';

/**
 * 登录状态管理
 * 用于跟踪登录对话框状态和请求队列
 */
class LoginManager {
  // 当前打开的登录对话框引用
  private static currentLoginDialog: MatDialogRef<LoginDialogComponent> | null = null;

  // 等待登录完成的请求队列
  private static pendingRequests: {
    request: HttpRequest<unknown>,
    next: HttpHandlerFn,
    resolve: (value: Observable<HttpEvent<unknown>>) => void,
    reject: (reason: any) => void
  }[] = [];

  /**
   * 检查是否有登录对话框正在打开
   */
  static isLoginDialogOpen(): boolean {
    return this.currentLoginDialog !== null;
  }

  /**
   * 设置当前登录对话框
   */
  static setCurrentLoginDialog(dialogRef: MatDialogRef<LoginDialogComponent>): void {
    this.currentLoginDialog = dialogRef;
  }

  /**
   * 清除当前登录对话框引用
   */
  static clearCurrentLoginDialog(): void {
    this.currentLoginDialog = null;
  }

  /**
   * 添加待处理的请求到队列
   */
  static addPendingRequest(request: HttpRequest<unknown>, next: HttpHandlerFn): Promise<Observable<HttpEvent<unknown>>> {
    return new Promise((resolve, reject) => {
      this.pendingRequests.push({request, next, resolve, reject});
    });
  }

  /**
   * 处理所有待处理的请求
   * @param success 登录是否成功
   * @param injector 注入器，用于获取服务
   * @param error 如果登录失败，传递错误对象
   */
  static processPendingRequests(success: boolean, injector: Injector, error?: HttpErrorResponse): void {
    // 复制队列并清空原队列，避免处理过程中有新请求加入导致无限循环
    const requests = [...this.pendingRequests];
    this.pendingRequests = [];

    if (success) {
      // 登录成功，处理所有待处理的请求
      const authService = injector.get(AuthService);
      const token = authService.getToken();

      requests.forEach(({request, next, resolve}) => {
        console.log('处理队列中的请求:', request.url);

        // 确保请求包含最新的授权头
        let newRequest = request.clone();
        if (token) {
          newRequest = request.clone({
            setHeaders: {
              Authorization: `Bearer ${token}`,
            },
          });
        }

        // 重新发送请求并解析Promise
        resolve(next(newRequest));
      });
    } else {
      // 登录失败，拒绝所有待处理的请求
      requests.forEach(({reject}) => {
        reject(error || new HttpErrorResponse({status: 401, statusText: '未授权'}));
      });
    }
  }
}

/**
 * HTTP错误拦截器
 * 处理API请求错误，特别是401未授权错误
 */
export const httpErrorInterceptor: HttpInterceptorFn = (
    request: HttpRequest<unknown>,
    next: HttpHandlerFn,
) => {
  const injector = inject(Injector);
  return next(request).pipe(
      catchError((error: HttpErrorResponse) => {
        const dialog = injector.get(MatDialog);

        // 处理401未授权错误
        if (error.status === 401) {
          // 避免无限循环：不拦截登录相关请求的401错误
          if (request.url.includes('auth/login') ||
              request.url.includes('auth/register') ||
              request.url.includes('auth/me')) {
            return throwError(() => error);
          }

          // 检查是否已经有登录对话框打开
          if (LoginManager.isLoginDialogOpen()) {
            console.log('登录对话框已打开，将请求添加到队列:', request.url);
            // 已有登录对话框打开，将请求添加到队列
            return new Observable<HttpEvent<unknown>>(observer => {
              LoginManager.addPendingRequest(request, next)
                .then(observable => {
                  // 当队列中的请求被处理时，订阅其结果并传递给当前Observable
                  observable.subscribe({
                    next: value => observer.next(value),
                    error: err => observer.error(err),
                    complete: () => observer.complete()
                  });
                })
                .catch(err => observer.error(err));
            });
          }

          // 获取窗口宽度
          const windowWidth = window.innerWidth;

          // 根据窗口宽度设置对话框宽度
          let dialogWidth = '400px'; // 默认宽度

          if (windowWidth < 480) {
            // 在小屏幕上使用更窄的宽度或百分比
            dialogWidth = '95%';
          }

          // 打开登录对话框
          const dialogRef = dialog.open(LoginDialogComponent, {
            width: dialogWidth,
            maxWidth: '95vw', // 最大宽度不超过视口宽度的95%
            disableClose: true,
          });

          // 记录当前打开的登录对话框
          LoginManager.setCurrentLoginDialog(dialogRef);

          // 登录对话框关闭后，如果返回true（表示登录成功），则重新发送原请求
          return dialogRef.afterClosed().pipe(
              switchMap(result => {
                // 清除当前登录对话框引用
                LoginManager.clearCurrentLoginDialog();

                if (result === true) {
                  console.log('登录成功，准备重新发送请求:', request.url);

                  // 添加短暂延迟，确保令牌已经保存并可用
                  return of(null).pipe(
                      delay(300), // 添加300ms延迟
                      switchMap(() => {
                        console.log('开始重新发送请求:', request.url);
                        // 克隆原请求并重新发送
                        // 确保请求包含最新的授权头
                        const authService = injector.get(AuthService);
                        const token = authService.getToken();

                        let newRequest = request.clone();
                        if (token) {
                          newRequest = request.clone({
                            setHeaders: {
                              Authorization: `Bearer ${token}`,
                            },
                          });
                        }

                        // 处理队列中的请求
                        LoginManager.processPendingRequests(true, injector);

                        return next(newRequest);
                      }),
                  );
                }

                // 如果用户取消登录或登录失败，则处理队列中的请求（全部拒绝）
                LoginManager.processPendingRequests(false, injector, error);

                // 继续抛出错误
                return throwError(() => error);
              }),
          );
        }

        // 对于其他错误，直接抛出
        return throwError(() => error);
      }),
  );
};
