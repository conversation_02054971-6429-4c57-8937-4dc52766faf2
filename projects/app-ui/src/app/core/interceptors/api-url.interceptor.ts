import {HttpHandlerFn, HttpInterceptorFn, HttpRequest} from '@angular/common/http';
import {environment} from '../../../environments/environment';

/**
 * API 拦截器
 * 补全 API 请求中的 URL，从而让各个 API 可以只指定相对路径
 */
export const apiUrlInterceptor: HttpInterceptorFn = (
    request: HttpRequest<unknown>,
    next: HttpHandlerFn,
) => {
  // 从环境配置中获取API基础URL
  const apiBaseUrl = environment.apiBaseUrl;

  // 检查请求 URL 是否已经包含 http:// 或 https:// 前缀
  if (request.url.startsWith('http://') || request.url.startsWith('https://')) {
    // 对于完整 URL，不添加前缀
    return next(request);
  }

  // 检查请求 URL 是否已经包含 API 前缀
  if (request.url.startsWith('/api/')) {
    // 如果已经包含 /api/ 前缀，则替换为环境配置中的 API 基础 URL
    const url = request.url.replace(/^\/api\//, `${apiBaseUrl}/`);
    const newRequest = request.clone({url});
    return next(newRequest);
  }

  return next(request);
};
