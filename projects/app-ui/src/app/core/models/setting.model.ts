/**
 * 设置接口
 */
export interface Settings {
  account: {
    email: string;
    phone: string;
    language: string;
    timezone: string;
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    activitySummary: boolean;
    marketingEmails: boolean;
  };
  privacy: {
    profileVisibility: string;
    showOnlineStatus: boolean;
    allowTagging: boolean;
    allowDataCollection: boolean;
  };
  appearance: {
    theme: string;
    fontSize: string;
    reducedMotion: boolean;
    highContrast: boolean;
  };
}

/**
 * 设置响应接口
 */
export interface SettingsResponse {
  settings: Settings;
}

/**
 * 更新设置响应接口
 */
export interface UpdateSettingsResponse {
  message: string;
  settings: Settings;
}
