/**
 * 认证相关的模型接口
 */

/**
 * 登录请求参数
 */
export interface LoginRequest {
  /** 用户名或邮箱 */
  username: string;
  /** 密码 */
  password: string;
  /** 是否记住登录状态 */
  rememberMe?: boolean;
}

/**
 * 注册请求参数
 */
export interface RegisterRequest {
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 密码 */
  password: string;
  /** 确认密码 */
  confirmPassword: string;
  /** 是否同意用户协议 */
  agreeTerms: boolean;
}

/**
 * 找回密码请求参数
 */
export interface ResetPasswordRequest {
  /** 邮箱 */
  email: string;
}

/**
 * 更新密码请求参数
 */
export interface UpdatePasswordRequest {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  newPassword: string;
  /** 确认新密码 */
  confirmPassword: string;
}

/**
 * 用户信息
 */
export interface User {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 头像 */
  avatar?: string;
  /** 创建时间 */
  createdAt: string;
}

/**
 * 认证响应
 */
export interface AuthResponse {
  /** 访问令牌 */
  token: string;
  /** 用户信息 */
  user: User;
  /** 过期时间（秒） */
  expiresIn: number;
}
