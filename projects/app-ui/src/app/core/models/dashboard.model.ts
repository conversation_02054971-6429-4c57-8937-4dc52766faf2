/**
 * 仪表盘统计数据接口
 */
export interface DashboardStat {
  id: number;
  title: string;
  value: number;
  icon: string;
  change: number;
  color: string;
}

/**
 * 仪表盘活动数据接口
 */
export interface DashboardActivity {
  id: number;
  type: string;
  title: string; // 添加 title 属性
  description: string; // 添加 description 属性
  content: string;
  time: string;
}

/**
 * 仪表盘响应接口
 */
export interface DashboardResponse {
  stats: DashboardStat[];
  activities: DashboardActivity[];
}
