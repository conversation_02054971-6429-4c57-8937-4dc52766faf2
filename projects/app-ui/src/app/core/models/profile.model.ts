/**
 * 个人资料接口
 */
export interface Profile {
  id: number;
  userId: number;
  name: string;
  avatar: string;
  email: string;
  phone: string;
  birthday: string;
  bio: string;
  location: string;
  website: string;
  joinDate: string;
  stats: {
    posts: number;
    followers: number;
    following: number;
  }; // 添加 stats 属性
}

/**
 * 活动接口
 */
export interface Activity {
  id: number;
  userId: number;
  type: string;
  title: string;
  content: string;
  time: string;
}

/**
 * 个人资料响应接口
 */
export interface ProfileResponse {
  profile: Profile;
  activities: Activity[];
}
