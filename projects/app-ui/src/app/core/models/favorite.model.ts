/**
 * 收藏项接口
 */
export interface Favorite {
  id: number;
  title: string;
  description: string;
  type: string;
  url: string;
  thumbnail: string;
  tags: string[];
  createdAt: string;
  author: string; // 添加 author 属性
  date: string; // 添加 date 属性
}

/**
 * 收藏响应接口
 */
export interface FavoriteResponse {
  favorites: Favorite[];
}

/**
 * 删除收藏响应接口
 */
export interface DeleteFavoriteResponse {
  message: string;
}
