import {Inject, Injectable, PLATFORM_ID} from '@angular/core';
import {isPlatformBrowser} from '@angular/common';
import {BehaviorSubject, Observable} from 'rxjs';

/**
 * 加载服务
 * 用于控制应用程序加载状态和加载界面的显示/隐藏
 */
@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  // 加载状态的行为主题
  private loadingSubject = new BehaviorSubject<boolean>(true);

  // 公开的加载状态可观察对象
  public loading$: Observable<boolean> = this.loadingSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
  }

  /**
   * 隐藏加载界面
   */
  public hideLoading(): void {
    if (isPlatformBrowser(this.platformId)) {
      // 移除加载界面
      const loadingElement = document.querySelector('.app-loading-wrapper');
      if (loadingElement) {
        // 添加淡出动画类
        loadingElement.classList.add('app-loading-done');

        // 动画结束后移除元素
        setTimeout(() => {
          loadingElement.remove();
        }, 500); // 与CSS动画时间匹配
      }

      this.loadingSubject.next(false);
    }
  }

  /**
   * 获取当前加载状态
   * @returns 当前是否正在加载
   */
  public isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
