import {Injectable} from '@angular/core';
import {environment} from '../../../environments/environment';
import {Environment} from '../../../environments/environment.interface';

/**
 * 环境配置服务
 * 提供对环境配置的访问
 */
@Injectable({
  providedIn: 'root',
})
export class EnvironmentService {
  /**
   * 获取当前环境配置
   */
  private env: Environment = environment;

  /**
   * 获取环境名称
   */
  get name(): string {
    return this.env.name;
  }

  /**
   * 检查是否为生产环境
   */
  get isProduction(): boolean {
    return this.env.production;
  }

  /**
   * 获取API基础URL
   */
  get apiBaseUrl(): string {
    return this.env.apiBaseUrl;
  }

  /**
   * 检查是否为调试模式
   */
  get isDebug(): boolean {
    return this.env.debug;
  }

  /**
   * 获取完整的环境配置
   */
  getEnvironment(): Environment {
    return this.env;
  }
}
