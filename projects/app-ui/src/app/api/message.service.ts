import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {MessageResponse} from '../core/models/message.model';

/**
 * 消息服务
 * 提供与消息相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class MessageService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取消息数据
   * @returns 通知和私信数据
   */
  getMessages(): Observable<MessageResponse> {
    return this.http.get<MessageResponse>('/api/messages');
  }
}
