import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {Profile, ProfileResponse} from '../core/models/profile.model';

/**
 * 个人资料服务
 * 提供与个人资料相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取个人资料
   * @returns 个人资料和活动数据
   */
  getProfile(): Observable<ProfileResponse> {
    return this.http.get<ProfileResponse>('/api/profile');
  }

  /**
   * 更新个人资料
   * @param data 要更新的资料数据
   * @returns 更新结果
   */
  updateProfile(data: Partial<Profile>): Observable<{ message: string, profile: Profile }> {
    return this.http.patch<{ message: string, profile: Profile }>('/api/profile', data);
  }
}
