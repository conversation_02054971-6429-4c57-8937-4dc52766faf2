import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ChatResponse} from '../core/models/chat.model';

/**
 * 聊天服务
 * 提供与网站聊天的功能
 */
@Injectable({
  providedIn: 'root',
})
export class ChatService {
  constructor(private http: HttpClient) {
  }

  /**
   * 发送聊天消息
   * @param message 用户消息
   * @returns 聊天回复
   */
  sendMessage(message: string): Observable<ChatResponse> {
    return this.http.post<ChatResponse>('/api/chat', {message});
  }
}
