import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {Settings, SettingsResponse, UpdateSettingsResponse} from '../core/models/setting.model';

/**
 * 设置服务
 * 提供与设置相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class SettingService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取设置数据
   * @returns 用户设置
   */
  getSettings(): Observable<SettingsResponse> {
    return this.http.get<SettingsResponse>('/api/settings');
  }

  /**
   * 更新设置
   * @param data 要更新的设置数据
   * @returns 更新结果
   */
  updateSettings(data: Settings): Observable<UpdateSettingsResponse> {
    return this.http.patch<UpdateSettingsResponse>('/api/settings', data);
  }
}
