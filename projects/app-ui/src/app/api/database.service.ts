import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';

/**
 * 数据库服务
 * 提供与数据库相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class DatabaseService {
  constructor(private http: HttpClient) {
  }

  /**
   * 重置数据库
   * @returns 重置结果
   */
  resetDatabase(): Observable<{ message: string }> {
    return this.http.get<{ message: string }>('/api/database/reset');
  }
}
