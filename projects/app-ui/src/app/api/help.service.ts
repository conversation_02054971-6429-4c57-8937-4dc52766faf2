import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {HelpResponse} from '../core/models/help.model';

/**
 * 帮助信息服务
 * 提供与帮助信息相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class HelpService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取帮助信息
   * @returns 常见问题和帮助分类
   */
  getHelp(): Observable<HelpResponse> {
    return this.http.get<HelpResponse>('/api/help');
  }
}
