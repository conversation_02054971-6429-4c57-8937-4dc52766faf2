import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ContactRequest, ContactResponse} from '../core/models/contact.model';

/**
 * 联系表单服务
 * 提供与联系表单相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class ContactService {
  constructor(private http: HttpClient) {
  }

  /**
   * 提交联系表单
   * @param data 联系表单数据
   * @returns 提交结果
   */
  submitContact(data: ContactRequest): Observable<ContactResponse> {
    return this.http.post<ContactResponse>('/api/contact', data);
  }
}
