import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {DashboardResponse} from '../core/models/dashboard.model';

/**
 * 仪表盘服务
 * 提供与仪表盘相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取仪表盘数据
   * @returns 仪表盘统计数据和活动数据
   */
  getDashboard(): Observable<DashboardResponse> {
    return this.http.get<DashboardResponse>('/api/dashboard');
  }
}
