import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {DeleteFavoriteResponse, FavoriteResponse} from '../core/models/favorite.model';

/**
 * 收藏服务
 * 提供与收藏相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class FavoriteService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取收藏数据
   * @returns 收藏列表
   */
  getFavorites(): Observable<FavoriteResponse> {
    return this.http.get<FavoriteResponse>('/api/favorites');
  }

  /**
   * 删除收藏
   * @param id 收藏ID
   * @returns 删除结果
   */
  deleteFavorite(id: number): Observable<DeleteFavoriteResponse> {
    return this.http.delete<DeleteFavoriteResponse>(`favorites/${id}`);
  }
}
