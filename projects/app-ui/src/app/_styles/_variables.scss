// CSS 变量定义
// 这个文件将被其他SCSS文件使用，使用@forward导出变量

:root {
  // 主题颜色 - 更现代的色彩方案
  --primary-color: #2563eb;
  --primary-light-color: #60a5fa;
  --primary-dark-color: #1e40af;
  --accent-color: #ec4899;
  --accent-light-color: #f472b6;
  --accent-dark-color: #db2777;

  // 功能色
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  // 字体
  /* 字体系列 - Windows和平板优先，中文环境优化 */
  --font-family-base: "Microsoft YaHei", "Microsoft JhengHei", "SimHei", "SimSun", "Segoe UI", <PERSON>o, "Helvetica Neue", Arial, "Noto Sans", "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans SC", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-heading: "Microsoft YaHei", "Microsoft JhengHei", "SimHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans SC", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-monospace: Consolas, "Microsoft YaHei Mono", "Microsoft JhengHei Mono", "SimSun-ExtB", "Courier New", "Liberation Mono", "Noto Sans Mono CJK SC", "Source Han Mono SC", monospace;

  // 文本颜色 - 更高对比度
  --text-primary-color: #111827;
  --text-secondary-color: #4b5563;
  --text-disabled-color: #9ca3af;
  --text-inverse-color: #ffffff;

  // 背景颜色
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --border-color: #e5e7eb;
  --divider-color: #d1d5db;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  // 字体大小 - 更现代的排版比例
  --font-size-xxs: 10px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --font-size-xxl: 28px;
  --font-size-xxxl: 32px;

  // 字体粗细
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  // 行高
  --line-height-compact: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.8;

  // 边框
  --border-width-thin: 1px;
  --border-width-medium: 2px;
  --border-width-thick: 3px;
  --border-style: solid;
  --border-thin: var(--border-width-thin) var(--border-style) var(--border-color);
  --border-medium: var(--border-width-medium) var(--border-style) var(--border-color);
  --border-accent: var(--border-width-medium) var(--border-style) var(--primary-color);

  // 圆角 - 更现代的圆角
  --border-radius-none: 0;
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 12px;
  --border-radius-full: 100px;

  // 阴影 - 更微妙、现代的阴影
  --shadow-none: none;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  // 动画
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.4s ease;
  --transition-enter: 0.25s cubic-bezier(0, 0, 0.2, 1);
  --transition-exit: 0.2s cubic-bezier(0.4, 0, 1, 1);
  --transition-emphasis: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  // 图标尺寸
  --icon-size-sm: 16px;
  --icon-size-md: 24px;
  --icon-size-lg: 32px;
  --icon-size-xl: 48px;

  // 组件特定
  --card-padding: 20px;
  --form-element-padding: 14px;
  --button-padding-x: 20px;
  --button-padding-y: 10px;
  --component-spacing: 24px;
  --section-spacing: 40px;

  --mat-sidenav-container-shape: 0;
  --mat-list-active-indicator-shape: 0;
}
