// Custom Theming for Angular Material
// For more information: https://material.angular.dev/guide/theming
@use '@angular/material' as mat;

// 定义 Material 主题
html {
  @include mat.theme((
          color: (
                  theme-type: light,
                  primary: mat.$azure-palette,
                  tertiary: mat.$blue-palette,
          ),
          typography: Roboto,
          density: 0,
  ));
}
