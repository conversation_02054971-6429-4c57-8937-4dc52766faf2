// 基础应用样式
// 这个文件不需要导入其他SCSS模块，因为它只使用CSS变量

// 全局 HTML 和 Body 样式
html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  background-color: var(--background-color);
  color: var(--text-primary-color);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast), transform var(--transition-fast);
  position: relative;

  &:hover {
    color: var(--primary-dark-color);
  }

  &:focus {
    outline: none;
    text-decoration: underline;
  }

  &:active {
    transform: translateY(1px);
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-compact);
}

h1 {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  letter-spacing: -0.5px;
}

h2 {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-semibold);
  letter-spacing: -0.3px;
}

h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// 段落样式
p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-normal);
}

// 强调文本
strong, b {
  font-weight: var(--font-weight-semibold);
}

// 小文本
small {
  font-size: var(--font-size-xs);
  color: var(--text-secondary-color);
}

// 代码样式
code, pre {
  font-family: var(--font-family-monospace);
  background-color: var(--divider-color);
  border-radius: var(--border-radius-sm);
}

code {
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

pre {
  padding: var(--spacing-md);
  overflow-x: auto;

  code {
    padding: 0;
    background-color: transparent;
  }
}

// 列表样式
ul, ol {
  padding-left: var(--spacing-lg);
  margin-top: 0;
  margin-bottom: var(--spacing-md);

  li {
    margin-bottom: var(--spacing-xs);
  }
}

// 表格样式
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);

  th, td {
    padding: var(--spacing-sm);
    border-bottom: var(--border-thin);
    text-align: left;
  }

  th {
    font-weight: var(--font-weight-semibold);
    background-color: var(--divider-color);
  }

  tr:hover {
    background-color: var(--divider-color);
  }
}

// 容器样式
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  margin-bottom: var(--section-spacing);
}

// 卡片容器
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--component-spacing);
  margin-bottom: var(--section-spacing);
}

// 辅助类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.bg-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse-color);
}

.bg-light {
  background-color: var(--divider-color);
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-center {
  align-items: center;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.mt-1 {
  margin-top: var(--spacing-xs);
}

.mt-2 {
  margin-top: var(--spacing-sm);
}

.mt-3 {
  margin-top: var(--spacing-md);
}

.mt-4 {
  margin-top: var(--spacing-lg);
}

.mt-5 {
  margin-top: var(--spacing-xl);
}

.mb-1 {
  margin-bottom: var(--spacing-xs);
}

.mb-2 {
  margin-bottom: var(--spacing-sm);
}

.mb-3 {
  margin-bottom: var(--spacing-md);
}

.mb-4 {
  margin-bottom: var(--spacing-lg);
}

.mb-5 {
  margin-bottom: var(--spacing-xl);
}

.ml-1 {
  margin-left: var(--spacing-xs);
}

.ml-2 {
  margin-left: var(--spacing-sm);
}

.ml-3 {
  margin-left: var(--spacing-md);
}

.ml-4 {
  margin-left: var(--spacing-lg);
}

.ml-5 {
  margin-left: var(--spacing-xl);
}

.mr-1 {
  margin-right: var(--spacing-xs);
}

.mr-2 {
  margin-right: var(--spacing-sm);
}

.mr-3 {
  margin-right: var(--spacing-md);
}

.mr-4 {
  margin-right: var(--spacing-lg);
}

.mr-5 {
  margin-right: var(--spacing-xl);
}

.icon-thumb {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
