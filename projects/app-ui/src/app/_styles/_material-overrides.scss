// Material 组件样式覆盖
@use '@angular/material' as mat;

// 按钮样式覆盖
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-text-button {
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.3px;
  border-radius: var(--border-radius-sm);
  transition: box-shadow var(--transition-normal), background-color var(--transition-normal);

  &:not(.mat-mdc-icon-button) {
    padding: var(--button-padding-y) var(--button-padding-x);
  }

  &:hover {
    // 移除位移效果，只保留阴影变化
  }

  &:active {
    // 移除位移效果，只保留阴影变化
  }

  .mat-icon {
    margin-right: var(--spacing-sm);
  }
}

.mat-mdc-raised-button {
  box-shadow: var(--shadow-md);

  &:hover {
    box-shadow: var(--shadow-lg);
  }

  &.mat-primary {
    background-color: var(--primary-color);
  }

  &.mat-accent {
    background-color: var(--accent-color);
  }

  &.mat-warn {
    background-color: var(--error-color);
  }
}

.mat-mdc-outlined-button {
  border: var(--border-thin);

  &.mat-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }

  &.mat-accent {
    color: var(--accent-color);
    border-color: var(--accent-color);
  }

  &.mat-warn {
    color: var(--error-color);
    border-color: var(--error-color);
  }
}

.mat-mdc-icon-button {
  border-radius: var(--border-radius-full);
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

// 卡片样式覆盖
.mat-mdc-card {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-lg);
  }

  .mat-mdc-card-header {
    padding: var(--card-padding) var(--card-padding) 0;
  }

  .mat-mdc-card-content {
    padding: var(--card-padding);
    font-size: var(--font-size-md);
    color: var(--text-secondary-color);
  }

  .mat-mdc-card-actions {
    padding: 0 var(--card-padding) var(--card-padding);
    margin: 0;
  }

  .mat-mdc-card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary-color);
    margin-bottom: var(--spacing-sm);
  }

  .mat-mdc-card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary-color);
  }
}

// 表单字段样式覆盖
.mat-mdc-form-field {
  width: 100%;
  margin-bottom: var(--spacing-md);

  .mat-mdc-form-field-flex {
    background-color: var(--surface-color);
    border-radius: var(--border-radius-sm);
  }

  .mat-mdc-text-field-wrapper {
    background-color: transparent;
  }

  .mat-mdc-form-field-infix {
    padding: var(--spacing-sm) 0;
  }

  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: var(--border-color);
    border-width: var(--border-width-thin);
    transition: border-color var(--transition-fast);
  }

  &.mat-focused {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--primary-color);
      border-width: var(--border-width-medium);
    }
  }

  .mat-mdc-form-field-error {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
  }
}

// 对话框样式覆盖
.mat-mdc-dialog-container {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;

  .mat-mdc-dialog-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary-color);
    padding: var(--card-padding) var(--card-padding) var(--spacing-sm);
  }

  .mat-mdc-dialog-content {
    padding: 0 var(--card-padding);
    color: var(--text-secondary-color);
    font-size: var(--font-size-md);
    max-height: 65vh;
  }

  .mat-mdc-dialog-actions {
    padding: var(--spacing-md) var(--card-padding);
    margin: 0;
  }
}

// 列表样式覆盖
.mat-mdc-list {
  padding: 0;

  .mat-mdc-list-item {
    height: auto;
    border-radius: var(--border-radius-sm);
    margin: var(--spacing-xs) 0;
    transition: background-color var(--transition-fast);

    &:hover {
      background-color: var(--divider-color);
    }

    .mat-mdc-list-item-title {
      font-weight: var(--font-weight-medium);
      color: var(--text-primary-color);
    }

    .mat-mdc-list-item-line {
      color: var(--text-secondary-color);
    }

    .mat-icon {
      margin-right: var(--spacing-md);
      color: var(--text-secondary-color);
    }
  }
}

// 选项卡样式覆盖
.mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: var(--border-thin);
  }

  .mat-mdc-tab {
    min-width: 100px;
    padding: 0 var(--spacing-md);
    height: 48px;
    opacity: 0.7;
    transition: opacity var(--transition-fast), background-color var(--transition-fast);

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }

    &.mat-mdc-tab-active {
      opacity: 1;
    }

    .mat-mdc-tab-label-content {
      font-weight: var(--font-weight-medium);
    }
  }

  .mat-mdc-tab-body-content {
    padding: var(--spacing-lg) 0;
  }

  .mdc-tab-indicator__content--underline {
    border-color: var(--primary-color);
    border-width: var(--border-width-medium);
  }
}

// 工具栏样式覆盖
.mat-mdc-toolbar {
  background-color: var(--primary-color);
  color: var(--text-inverse-color);
  box-shadow: var(--shadow-md);

  &.mat-primary {
    background-color: var(--primary-color);
  }

  &.mat-accent {
    background-color: var(--accent-color);
  }

  &.mat-warn {
    background-color: var(--error-color);
  }

  .mat-mdc-button,
  .mat-mdc-icon-button {
    color: var(--text-inverse-color);
  }
}

// 进度条样式覆盖
.mat-mdc-progress-bar {
  height: 4px;
  border-radius: var(--border-radius-full);

  .mat-mdc-progress-bar-fill::after {
    background-color: var(--primary-color);
  }

  &.mat-accent .mat-mdc-progress-bar-fill::after {
    background-color: var(--accent-color);
  }

  &.mat-warn .mat-mdc-progress-bar-fill::after {
    background-color: var(--error-color);
  }
}

// 复选框样式覆盖
.mat-mdc-checkbox {
  .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .mdc-checkbox__background {
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast), border-color var(--transition-fast);
  }
}

// 单选按钮样式覆盖
.mat-mdc-radio-button {
  .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
    border-color: var(--primary-color);
  }

  .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .mdc-radio__background {
    transition: all var(--transition-fast);
  }
}

// 菜单样式覆盖
.mat-mdc-menu-panel {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);

  .mat-mdc-menu-content {
    padding: var(--spacing-xs) 0;
  }

  .mat-mdc-menu-item {
    font-size: var(--font-size-md);
    height: 48px;
    padding: 0 var(--spacing-md);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 48px;

    .mat-icon {
      margin-right: var(--spacing-md);
      color: var(--text-secondary-color);
    }

    span {
      display: flex;
      align-items: center;
      height: 100%;
      font-weight: var(--font-weight-medium);
      color: var(--text-primary-color);
    }

    &:hover {
      background-color: var(--divider-color);
    }
  }
}

// 徽章样式覆盖
.mat-badge-content {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xxs);
  background-color: var(--accent-color);
}

// 分割线样式覆盖
.mat-divider {
  border-top-color: var(--divider-color);
}

// 滑块样式覆盖
.mat-mdc-slide-toggle {
  .mdc-switch__track {
    background-color: rgba(0, 0, 0, 0.25);
  }

  .mdc-switch__handle-track {
    transition: transform var(--transition-normal);
  }

  &.mat-mdc-slide-toggle-checked {
    .mdc-switch__track {
      background-color: var(--primary-color);
    }
  }
}
