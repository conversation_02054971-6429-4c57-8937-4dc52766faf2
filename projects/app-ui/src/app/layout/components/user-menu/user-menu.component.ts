import {Compo<PERSON>, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatButtonModule} from '@angular/material/button';
import {MatMenuModule} from '@angular/material/menu';
import {MatDividerModule} from '@angular/material/divider';
import {MatDialog} from '@angular/material/dialog';
import {RouterLink} from '@angular/router';
import {Subscription} from 'rxjs';
import {AuthService} from '../../../core/services/auth.service';
import {LoginDialogComponent} from '../../../auth/login-dialog/login-dialog.component';
import {RegisterDialogComponent} from '../../../auth/register-dialog/register-dialog.component';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-user-menu',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    RouterLink,
    MatIconModule,
  ],
  templateUrl: './user-menu.component.html',
  styleUrl: './user-menu.component.scss',
})
export class UserMenuComponent implements OnInit, OnDestroy {
  // 用户登录状态
  isLoggedIn = false;

  // 用户名
  userName = '';

  // 订阅
  private authSubscription: Subscription | null = null;

  constructor(
      private authService: AuthService,
      private dialog: MatDialog,
  ) {
  }

  ngOnInit(): void {
    // 订阅认证状态变化
    this.authSubscription = this.authService.currentUser$.subscribe(user => {
      this.isLoggedIn = !!user;
      this.userName = user?.username || '';
    });

    // 检查初始认证状态
    this.checkAuthStatus();
  }

  ngOnDestroy(): void {
    // 取消订阅
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
      this.authSubscription = null;
    }
  }

  /**
   * 检查认证状态
   */
  checkAuthStatus(): void {
    this.authService.checkAuthStatus().subscribe();
  }

  /**
   * 打开登录对话框
   */
  openLoginDialog(): void {
    // 获取窗口宽度
    const windowWidth = window.innerWidth;

    // 根据窗口宽度设置对话框宽度
    let dialogWidth = '400px'; // 默认宽度

    if (windowWidth < 480) {
      // 在小屏幕上使用更窄的宽度或百分比
      dialogWidth = '95%';
    }

    this.dialog.open(LoginDialogComponent, {
      width: dialogWidth,
      maxWidth: '95vw', // 最大宽度不超过视口宽度的95%
    });
  }

  /**
   * 打开注册对话框
   */
  openRegisterDialog(): void {
    // 获取窗口宽度
    const windowWidth = window.innerWidth;

    // 根据窗口宽度设置对话框宽度
    let dialogWidth = '400px'; // 默认宽度

    if (windowWidth < 480) {
      // 在小屏幕上使用更窄的宽度或百分比
      dialogWidth = '95%';
    }

    this.dialog.open(RegisterDialogComponent, {
      width: dialogWidth,
      maxWidth: '95vw', // 最大宽度不超过视口宽度的95%
    });
  }

  /**
   * 登出
   */
  logout(): void {
    this.authService.logout().subscribe();
  }
}
