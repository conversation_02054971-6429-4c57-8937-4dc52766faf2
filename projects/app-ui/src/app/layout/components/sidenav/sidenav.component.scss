:host {
  display: block;
}

.mat-toolbar {
  background-color: var(--primary-color);
  color: var(--text-inverse-color);
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  height: var(--toolbar-height);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-lg);
}

.mat-nav-list {
  padding: var(--spacing-md) var(--spacing-sm);

  a {
    border-radius: 0;
    height: 48px;
    font-weight: var(--font-weight-regular);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-md);
    color: var(--text-primary-color);
    position: relative;
    overflow: hidden;

    mat-icon {
      margin-right: var(--spacing-md);
      width: var(--icon-size-md);
      height: var(--icon-size-md);
      color: var(--text-secondary-color);
      transition: color var(--transition-fast);
    }

    &:hover {
      background-color: var(--divider-color);

      mat-icon {
        color: var(--primary-color);
      }
    }

    &.active {
      background-color: rgba(37, 99, 235, 0.08);
      color: var(--primary-color);
      font-weight: var(--font-weight-medium);
      border-left: 3px solid var(--primary-color);

      mat-icon {
        color: var(--primary-color);
      }
    }
  }
}

.mat-divider {
  margin: var(--spacing-md) var(--spacing-sm);
  border-color: var(--divider-color);
}
