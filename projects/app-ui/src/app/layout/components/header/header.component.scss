:host {
  display: block;
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--shadow-md);
  height: var(--toolbar-height);
  padding: 0 var(--spacing-md);
  display: flex;
  justify-content: space-between;
  background-color: var(--primary-color);
  color: var(--text-inverse-color);

  .toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
  }

  .toolbar-left {
    button {
      margin-right: var(--spacing-sm);

      mat-icon {
        color: var(--text-inverse-color);
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }

    .app-logo {
      display: flex;
      align-items: center;
      margin-right: var(--spacing-md);
      transition: transform var(--transition-fast);

      &:hover {
        transform: scale(1.05);
      }

      img {
        display: block;
        width: 36px;
        height: 36px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    .app-title {
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-lg);
      margin-right: var(--spacing-xl);
      white-space: nowrap;
      letter-spacing: 0.5px;
      color: var(--text-inverse-color);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  .toolbar-right {
    button {
      margin-left: var(--spacing-sm);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: scale(0);
        transition: transform var(--transition-fast);
      }

      &:hover::after {
        transform: scale(1);
      }
    }

    mat-icon {
      color: var(--text-inverse-color);
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
}

@media (max-width: 959px) {
  .toolbar-left {
    .app-title {
      font-size: var(--font-size-md) !important;
      margin-right: var(--spacing-md) !important;
    }
  }
}

@media (max-width: 599px) {
  .mat-toolbar.mat-primary {
    padding: 0 var(--spacing-sm);
    height: var(--toolbar-height);

    .app-title {
      font-size: var(--font-size-md);
      margin-right: 0 !important;
    }

    .toolbar-right button {
      margin-left: var(--spacing-xs);
    }
  }
}
