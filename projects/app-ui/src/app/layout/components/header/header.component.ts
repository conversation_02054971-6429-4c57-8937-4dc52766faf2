import {Component, EventEmitter, Input, Output} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatButtonModule} from '@angular/material/button';
import {MatBadgeModule} from '@angular/material/badge';
import {RouterLink} from '@angular/router';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatBadgeModule,
    NgOptimizedImage,
    MatIconModule,
    RouterLink,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent {
  @Input() isHandset = false;
  @Input() notificationCount = 0;
  @Output() toggleSidenav = new EventEmitter<void>();
  @Output() toggleMobileSearch = new EventEmitter<void>();

  onToggleSidenav(): void {
    this.toggleSidenav.emit();
  }

  onToggleMobileSearch(): void {
    this.toggleMobileSearch.emit();
  }
}
