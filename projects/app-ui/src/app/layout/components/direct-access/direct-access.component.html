<!-- 桌面直达功能 -->
@if (!isHandset) {
  <div class="direct-access-container">
    <div class="input-container">
      <form (submit)="sendMessage($event)">
        <mat-form-field appearance="outline" class="direct-access-field">
          <input
              matInput
              [(ngModel)]="userInput"
              name="directAccessInput"
              placeholder="直接与网站对话..."
              aria-label="直达功能"
              (focus)="showChatPanel = true">
          <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="toggleChatPanel($event)"
              [attr.aria-label]="showChatPanel ? '关闭对话' : '打开对话'">
            <mat-icon svgIcon="smart-toy" matButtonIcon></mat-icon>
          </button>
        </mat-form-field>
      </form>
    </div>

    <!-- 聊天面板 -->
    @if (showChatPanel) {
      <div class="chat-panel" (click)="$event.stopPropagation()">
        <div class="chat-header">
          <h3>智能助手</h3>
          <button mat-icon-button (click)="clearChat($event)" aria-label="清空聊天">
            <mat-icon svgIcon="delete" matButtonIcon></mat-icon>
          </button>
        </div>
        <div class="chat-messages">
          @for (message of chatHistory; track message.id) {
            <div class="message" [ngClass]="{'user-message': message.isUser, 'system-message': !message.isUser}">
              <div class="message-content">
                <p>{{ message.text }}</p>
                @if (message.links && message.links.length > 0) {
                  <div class="message-links">
                    @for (link of message.links; track link.url) {
                      <a
                          mat-button
                          color="primary"
                          (click)="navigateToLink(link.url)"
                          matRipple>
                        {{ link.text }}
                      </a>
                    }
                  </div>
                }
              </div>
            </div>
          }
          @if (isLoading) {
            <div class="message system-message">
              <div class="message-content loading">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          }
        </div>
        <div class="chat-input">
          <form (submit)="sendMessage($event)">
            <mat-form-field appearance="outline" class="chat-field">
              <input
                  matInput
                  [(ngModel)]="userInput"
                  name="chatInput"
                  placeholder="输入您的问题..."
                  aria-label="聊天输入框">
              <button
                  mat-icon-button
                  matSuffix
                  type="submit"
                  [disabled]="!userInput.trim() || isLoading"
                  aria-label="发送消息">
                <mat-icon svgIcon="send" matButtonIcon></mat-icon>
              </button>
            </mat-form-field>
          </form>
        </div>
      </div>
    }
  </div>
}

<!-- 移动设备上的直达功能 -->
@if (isHandset && showMobileSearch) {
  <div class="mobile-direct-access-container">
    <div class="mobile-chat-panel" (click)="$event.stopPropagation()">
      <div class="chat-header">
        <h3>智能助手</h3>
        <div class="header-actions">
          <button mat-icon-button (click)="clearChat($event)" aria-label="清空聊天">
            <mat-icon svgIcon="delete" matButtonIcon></mat-icon>
          </button>
          <button mat-icon-button (click)="toggleMobileSearch($event)" aria-label="关闭对话">
            <mat-icon svgIcon="close" matButtonIcon></mat-icon>
          </button>
        </div>
      </div>
      <div class="chat-messages">
        @for (message of chatHistory; track message.id) {
          <div class="message" [ngClass]="{'user-message': message.isUser, 'system-message': !message.isUser}">
            <div class="message-content">
              <p>{{ message.text }}</p>
              @if (message.links && message.links.length > 0) {
                <div class="message-links">
                  @for (link of message.links; track link.url) {
                    <a
                        mat-button
                        color="primary"
                        (click)="navigateToLink(link.url)"
                        matRipple>
                      {{ link.text }}
                    </a>
                  }
                </div>
              }
            </div>
          </div>
        }
        @if (isLoading) {
          <div class="message system-message">
            <div class="message-content loading">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        }
      </div>
      <div class="chat-input">
        <form (submit)="sendMessage($event)">
          <mat-form-field appearance="outline" class="chat-field">
            <input
                matInput
                [(ngModel)]="userInput"
                name="mobileChatInput"
                placeholder="输入您的问题..."
                aria-label="移动端聊天输入框">
            <button
                mat-icon-button
                matSuffix
                type="submit"
                [disabled]="!userInput.trim() || isLoading"
                aria-label="发送消息">
              <mat-icon svgIcon="send" matButtonIcon></mat-icon>
            </button>
          </mat-form-field>
        </form>
      </div>
    </div>
  </div>
}
