import {Compo<PERSON>, ElementRef, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatCardModule} from '@angular/material/card';
import {MatRippleModule} from '@angular/material/core';
import {Router} from '@angular/router';
import {ChatService} from '../../../api/chat.service';
import {ChatLink, ChatMessage} from '../../../core/models/chat.model';
import {Subject, takeUntil} from 'rxjs';
import {generateId} from '../../../shared/utils/generate-id';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-direct-access',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatRippleModule,
    MatIconModule,
  ],
  templateUrl: './direct-access.component.html',
  styleUrl: './direct-access.component.scss',
})
export class DirectAccessComponent implements OnInit, OnDestroy {
  @Input() isHandset = false;

  @Input() showMobileSearch = false;

  @Output() toggleMobileSearchEvent = new EventEmitter<void>();

  // 用户输入
  userInput = '';

  // 聊天历史
  chatHistory: ChatMessage[] = [];

  // 是否显示聊天面板
  showChatPanel = false;

  // 是否正在加载
  isLoading = false;

  // 组件销毁时的信号
  private destroy$ = new Subject<void>();

  constructor(
      private chatService: ChatService,
      private router: Router,
      private elementRef: ElementRef,
  ) {
  }

  /**
   * 监听document的点击事件
   * 如果点击的是组件外部，则关闭聊天面板
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // 检查点击事件是否发生在组件外部
    if (this.showChatPanel && !this.elementRef.nativeElement.contains(event.target)) {
      this.showChatPanel = false;

      // 如果是移动端，也关闭移动搜索
      if (this.isHandset && this.showMobileSearch) {
        this.toggleMobileSearchEvent.emit();
      }
    }
  }

  ngOnInit(): void {
    // 添加欢迎消息
    if (this.chatHistory.length === 0) {
      this.addSystemMessage('您好！我是智能助手，可以帮您快速找到需要的功能。请问有什么可以帮您？');
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 切换聊天面板显示状态
   */
  toggleChatPanel(event?: MouseEvent): void {
    // 阻止事件冒泡，防止触发document的点击事件
    if (event) {
      event.stopPropagation();
    }

    this.showChatPanel = !this.showChatPanel;

    // 如果打开面板且没有历史消息，添加欢迎消息
    if (this.showChatPanel && this.chatHistory.length === 0) {
      this.addSystemMessage('您好！我是智能助手，可以帮您快速找到需要的功能。请问有什么可以帮您？');
    }
  }

  /**
   * 切换移动搜索显示状态
   * 这个方法会通知父组件关闭移动搜索
   */
  toggleMobileSearch(event?: MouseEvent): void {
    // 阻止事件冒泡，防止触发document的点击事件
    if (event) {
      event.stopPropagation();
    }

    // 发出事件通知父组件
    this.toggleMobileSearchEvent.emit();
  }

  /**
   * 发送消息
   */
  sendMessage(event?: Event): void {
    if (event) {
      event.preventDefault();
    }

    if (!this.userInput.trim()) {
      return;
    }

    // 添加用户消息到历史
    this.addUserMessage(this.userInput);

    // 保存用户输入并清空输入框
    const message = this.userInput;
    this.userInput = '';

    // 设置加载状态
    this.isLoading = true;

    // 调用聊天服务
    this.chatService.sendMessage(message)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            // 添加系统回复到历史
            this.addSystemMessage(response.text, response.links);
            this.isLoading = false;
          },
          error: (error) => {
            console.error('发送消息失败:', error);
            this.addSystemMessage('抱歉，我遇到了一些问题，无法回复您的消息。请稍后再试。');
            this.isLoading = false;
          },
        });
  }

  /**
   * 添加用户消息到历史
   * @param text 消息文本
   */
  private addUserMessage(text: string): void {
    this.chatHistory.push({
      id: generateId(),
      text,
      isUser: true,
      timestamp: new Date(),
    });
  }

  /**
   * 添加系统消息到历史
   * @param text 消息文本
   * @param links 链接列表
   */
  private addSystemMessage(text: string, links?: ChatLink[]): void {
    this.chatHistory.push({
      id: generateId(),
      text,
      isUser: false,
      timestamp: new Date(),
      links,
    });
  }

  /**
   * 点击链接
   * @param url 链接URL
   */
  navigateToLink(url: string): void {
    this.router.navigateByUrl(url);
    // 如果是移动设备，关闭聊天面板
    if (this.isHandset) {
      this.showChatPanel = false;
    }
  }

  /**
   * 清空聊天历史
   */
  clearChat(event?: MouseEvent): void {
    // 阻止事件冒泡，防止触发document的点击事件
    if (event) {
      event.stopPropagation();
    }

    this.chatHistory = [];
    this.addSystemMessage('聊天记录已清空。有什么可以帮您的吗？');
  }
}
