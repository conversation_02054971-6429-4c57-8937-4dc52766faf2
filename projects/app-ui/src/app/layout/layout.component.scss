:host {
  --toolbar-height: 64px;
}

.sidenav-container {
  height: 100%;
}

.sidenav {
  width: 260px;
  box-shadow: var(--shadow-md);
  background-color: var(--surface-color);
  border-right: none;

  // 移动端默认隐藏
  @media (max-width: 599px) {
    transform: translateX(-100%);
    visibility: hidden;

    &.mat-drawer-opened {
      transform: translateX(0);
      visibility: visible;
    }
  }
}

.mat-sidenav-content {
  background-color: var(--background-color);
  min-height: 100vh;
  transition: padding var(--transition-normal);
}

@media (max-width: 599px) {
  :host {
    --toolbar-height: 48px;
  }

  .sidenav {
    width: 85%;
    max-width: 280px;
  }
}
