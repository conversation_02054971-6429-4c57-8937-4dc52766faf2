<mat-sidenav-container class="sidenav-container">
  <mat-sidenav #drawer class="sidenav" fixedInViewport
               [attr.role]="isHandset ? 'dialog' : 'navigation'"
               [mode]="isHandset ? 'over' : 'side'"
               [opened]="!isHandset">
    <app-sidenav [isHandset]="isHandset"></app-sidenav>
  </mat-sidenav>

  <mat-sidenav-content>
    <app-header
        [isHandset]="isHandset"
        [notificationCount]="notificationCount"
        (toggleSidenav)="toggleSidenav()"
        (toggleMobileSearch)="toggleMobileSearch()">

      <!-- 直达功能插槽 -->
      <app-direct-access search-bar
                         [isHandset]="isHandset"
                         [showMobileSearch]="false">
      </app-direct-access>

      <!-- 用户菜单插槽 -->
      <app-user-menu user-menu></app-user-menu>
    </app-header>

    <!-- 移动设备上的直达功能 -->
    @if (isHandset) {
      <app-direct-access
          [isHandset]="isHandset"
          [showMobileSearch]="showMobileSearch"
          (toggleMobileSearchEvent)="toggleMobileSearch()">
      </app-direct-access>
    }

    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>
