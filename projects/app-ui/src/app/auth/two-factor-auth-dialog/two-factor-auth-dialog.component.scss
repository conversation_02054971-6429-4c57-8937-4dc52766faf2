.two-factor-auth-dialog {
  padding: 24px;
  max-width: 500px;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.dialog-header {
  text-align: center;
  margin-bottom: 24px;

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }

    mat-icon {
      font-size: 24px;
      color: var(--primary-color);
    }
  }

  .dialog-subtitle {
    margin: 0;
    color: var(--text-secondary-color);
    font-size: 14px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;

  p {
    margin-top: 16px;
    color: var(--text-secondary-color);
  }
}

.error-message {
  background-color: var(--error-lighter-color);
  color: var(--error-color);
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 16px;
}

.two-factor-content {
  margin-bottom: 24px;
}

.status-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--surface-variant-color);
  border-radius: 8px;

  .status-info {
    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 500;
    }

    p {
      margin: 0;
      color: var(--text-secondary-color);
    }
  }
}

.step-content {
  padding: 16px 0;

  p {
    margin: 0 0 16px 0;
    line-height: 1.5;
  }

  .hint {
    font-size: 14px;
    color: var(--text-secondary-color);
  }

  .warning {
    color: var(--warning-color);
    font-weight: 500;
  }
}

.qr-code {
  display: flex;
  justify-content: center;
  margin: 16px 0;

  img {
    max-width: 200px;
    height: auto;
    border: 1px solid var(--divider-color);
    padding: 8px;
    background-color: white;
  }
}

.form-field {
  width: 100%;
  max-width: 200px;
}

.recovery-codes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  margin: 16px 0;
  padding: 16px;
  background-color: var(--surface-variant-color);
  border-radius: 4px;

  .recovery-code {
    font-family: monospace;
    padding: 8px;
    background-color: var(--surface-color);
    border: 1px solid var(--divider-color);
    border-radius: 4px;
    text-align: center;
  }
}

.step-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;

  button {
    min-width: 80px;
  }

  button[color="primary"] {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-spinner {
      margin-right: 8px;
    }
  }
}

.enabled-info {
  padding: 16px;
  background-color: var(--surface-variant-color);
  border-radius: 8px;
  margin-bottom: 16px;

  p {
    margin: 0 0 8px 0;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
}

.success-message {
  text-align: center;
  padding: 24px 16px;

  mat-icon {
    font-size: 48px;
    color: var(--success-color);
    display: block;
    margin: 0 auto 16px;
  }

  h3 {
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 500;
  }

  p {
    margin: 0 0 24px 0;
    line-height: 1.5;
    color: var(--text-secondary-color);
  }
}

.success-actions {
  display: flex;
  justify-content: center;
}

// 覆盖Material Dialog样式
:host {
  display: block;

  ::ng-deep .mat-mdc-dialog-container {
    padding: 0 !important;

    .mdc-dialog__surface {
      padding: 0 !important;
    }

    .mat-mdc-dialog-surface {
      padding: 0 !important;
    }

    .mat-mdc-dialog-content {
      padding: 0 !important;
      margin: 0 !important;
      display: block !important;
    }
  }
}

// 响应式调整
@media (max-width: 500px) {
  .two-factor-auth-dialog {
    padding: 16px;
  }

  .dialog-header {
    margin-bottom: 16px;

    .dialog-title h2 {
      font-size: 20px;
    }
  }

  .recovery-codes {
    grid-template-columns: 1fr;
  }
}
