<div class="two-factor-auth-dialog">
  <div class="dialog-header">
    <div class="dialog-title">
      <h2>两步验证</h2>
    </div>
    <p class="dialog-subtitle">增强您的账号安全性</p>
  </div>

  <!-- 加载状态 -->
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误消息 -->
  @if (errorMessage) {
    <div class="error-message">
      {{ errorMessage }}
    </div>
  }

  <!-- 两步认证内容 -->
  @if (!isLoading && !isSuccess) {
    <div class="two-factor-content">
      <div class="status-section">
        <div class="status-info">
          <h3>两步验证状态</h3>
          <p>{{ twoFactorEnabled ? '已启用' : '未启用' }}</p>
        </div>
        <mat-slide-toggle
            [checked]="twoFactorEnabled"
            (change)="toggleTwoFactor($event.checked)"
            color="primary"
            [disabled]="isSubmitting">
        </mat-slide-toggle>
      </div>

      <!-- 启用两步认证向导 -->
      @if (!twoFactorEnabled) {
        <mat-stepper orientation="vertical" linear>
          <!-- 步骤1：扫描二维码 -->
          <mat-step label="扫描二维码" state="qr">
            <div class="step-content">
              <p>使用身份验证器应用（如Google Authenticator、Microsoft Authenticator）扫描下方二维码：</p>
              <div class="qr-code">
                <img [src]="qrCodeUrl" alt="两步验证二维码">
              </div>
              <p class="hint">或者手动输入密钥：ABCDEFGHIJKLMNOP</p>
              <div class="step-actions">
                <button mat-button matStepperNext>下一步</button>
              </div>
            </div>
          </mat-step>

          <!-- 步骤2：输入验证码 -->
          <mat-step label="输入验证码" state="verify">
            <div class="step-content">
              <p>打开身份验证器应用，输入显示的6位验证码：</p>
              <form [formGroup]="verificationForm">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>验证码</mat-label>
                  <input matInput formControlName="verificationCode" type="text" maxlength="6" autocomplete="off">
                  @if (verificationForm.get('verificationCode')?.invalid && verificationForm.get('verificationCode')?.touched) {
                    @if (verificationForm.get('verificationCode')?.errors?.['required']) {
                      <mat-error>请输入验证码</mat-error>
                    } @else if (verificationForm.get('verificationCode')?.errors?.['pattern']) {
                      <mat-error>验证码必须是6位数字</mat-error>
                    }
                  }
                </mat-form-field>
              </form>
              <div class="step-actions">
                <button mat-button matStepperPrevious>上一步</button>
                <button mat-button matStepperNext>下一步</button>
              </div>
            </div>
          </mat-step>

          <!-- 步骤3：保存恢复代码 -->
          <mat-step label="保存恢复代码" state="backup">
            <div class="step-content">
              <p>请保存以下恢复代码，以便在无法使用身份验证器时恢复账号访问：</p>
              <div class="recovery-codes">
                @for (code of recoveryCodes; track code) {
                  <div class="recovery-code">{{ code }}</div>
                }
              </div>
              <p class="warning">请将这些代码保存在安全的地方，它们只会显示一次！</p>
              <div class="step-actions">
                <button mat-button matStepperPrevious>上一步</button>
                @if (isSubmitting) {
                  <button mat-raised-button color="primary" [disabled]="true">
                    <mat-spinner diameter="20"></mat-spinner>
                    <span>启用中...</span>
                  </button>
                } @else {
                  <button mat-raised-button color="primary" (click)="verifyAndEnable()">
                    <mat-icon matButtonIcon svgIcon="security"></mat-icon>
                    <span>启用两步验证</span>
                  </button>
                }
              </div>
            </div>
          </mat-step>
        </mat-stepper>
      }

      <!-- 已启用两步认证信息 -->
      @if (twoFactorEnabled) {
        <div class="enabled-info">
          <p>您的账号已启用两步验证，每次登录时都需要输入验证码。</p>
          <p>如果您想禁用两步验证，请点击上方的开关。</p>
        </div>
      }
    </div>

    <div class="dialog-actions">
      <button mat-button (click)="onCancel()">关闭</button>
    </div>
  }

  <!-- 成功消息 -->
  @if (isSuccess) {
    <div class="success-message">
      @if (twoFactorEnabled) {
        <mat-icon svgIcon="verified-user"></mat-icon>
        <h3>两步验证已成功启用</h3>
        <p>您的账号现在受到额外的安全保护。每次登录时，您都需要输入身份验证器应用中的验证码。</p>
      } @else {
        <mat-icon svgIcon="security"></mat-icon>
        <h3>两步验证已禁用</h3>
        <p>您的账号不再需要验证码登录。为了提高安全性，我们建议您保持两步验证的启用状态。</p>
      }
      <div class="success-actions">
        <button mat-raised-button color="primary" (click)="onClose()">确定</button>
      </div>
    </div>
  }
</div>
