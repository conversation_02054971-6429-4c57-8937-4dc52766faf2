import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatStepperModule} from '@angular/material/stepper';
import {AuthService} from '../../core/services/auth.service';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-two-factor-auth-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatStepperModule,
    MatIconModule,
  ],
  templateUrl: './two-factor-auth-dialog.component.html',
  styleUrl: './two-factor-auth-dialog.component.scss',
  host: {
    class: 'two-factor-auth-dialog-host',
  },
})
export class TwoFactorAuthDialogComponent implements OnInit {
  // 两步认证状态
  twoFactorEnabled = false;

  // 验证表单
  verificationForm: FormGroup;

  // 是否正在加载
  isLoading = true;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  // 是否已提交成功
  isSuccess = false;

  // 二维码图片URL（实际应用中应该从后端获取）
  qrCodeUrl = 'data:image/png;base64,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';

  // 恢复代码（实际应用中应该从后端获取）
  recoveryCodes = [
    'ABCD-EFGH-IJKL',
    'MNOP-QRST-UVWX',
    'YZAB-CDEF-GHIJ',
    'KLMN-OPQR-STUV',
    'WXYZ-ABCD-EFGH',
    'IJKL-MNOP-QRST',
    'UVWX-YZAB-CDEF',
    'GHIJ-KLMN-OPQR',
  ];

  constructor(
      private dialogRef: MatDialogRef<TwoFactorAuthDialogComponent>,
      private formBuilder: FormBuilder,
      private authService: AuthService,
  ) {
    // 初始化验证表单
    this.verificationForm = this.formBuilder.group({
      verificationCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
    });
  }

  ngOnInit(): void {
    // 加载两步认证状态
    this.loadTwoFactorStatus();
  }

  /**
   * 加载两步认证状态
   */
  loadTwoFactorStatus(): void {
    this.isLoading = true;
    this.errorMessage = '';

    // 模拟从服务获取两步认证状态
    setTimeout(() => {
      // 这里应该调用实际的服务方法
      this.twoFactorEnabled = false; // 假设默认未启用
      this.isLoading = false;
    }, 1000);
  }

  /**
   * 切换两步认证状态
   */
  toggleTwoFactor(enabled: boolean): void {
    if (enabled === this.twoFactorEnabled) {
      return;
    }

    if (enabled) {
      // 启用两步认证，不立即提交，等待验证码验证
      this.twoFactorEnabled = false; // 保持关闭状态，直到验证成功
    } else {
      // 禁用两步认证，立即提交
      this.disableTwoFactor();
    }
  }

  /**
   * 禁用两步认证
   */
  disableTwoFactor(): void {
    this.isSubmitting = true;
    this.errorMessage = '';

    // 模拟禁用两步认证
    setTimeout(() => {
      // 这里应该调用实际的服务方法
      this.twoFactorEnabled = false;
      this.isSubmitting = false;
      this.isSuccess = true;
    }, 1000);
  }

  /**
   * 验证并启用两步认证
   */
  verifyAndEnable(): void {
    if (this.verificationForm.invalid) {
      this.verificationForm.markAllAsTouched();
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    // 模拟验证并启用两步认证
    setTimeout(() => {
      // 这里应该调用实际的服务方法
      const code = this.verificationForm.get('verificationCode')?.value;

      // 假设验证码为 123456 是有效的
      if (code === '123456') {
        this.twoFactorEnabled = true;
        this.isSubmitting = false;
        this.isSuccess = true;
      } else {
        this.isSubmitting = false;
        this.errorMessage = '验证码无效，请重试';
      }
    }, 1000);
  }

  /**
   * 关闭对话框
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * 关闭对话框并返回成功
   */
  onClose(): void {
    this.dialogRef.close(true);
  }
}
