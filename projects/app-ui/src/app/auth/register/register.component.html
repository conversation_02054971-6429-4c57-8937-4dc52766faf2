<div class="register-container">
  <div class="register-card">
    <div class="card-header">
      <div class="header-title">
        <mat-icon svgIcon="person" aria-label="Register icon"></mat-icon>
        <h1>注册账号</h1>
      </div>
      <p class="header-subtitle">创建一个新账号以访问所有功能</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
      <!-- 错误消息 -->
      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }

      <!-- 用户名 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>用户名</mat-label>
        <input matInput formControlName="username" type="text" autocomplete="username">
        @if (registerForm.get('username')?.invalid && registerForm.get('username')?.touched) {
          @if (registerForm.get('username')?.errors?.['required']) {
            <mat-error>请输入用户名</mat-error>
          } @else if (registerForm.get('username')?.errors?.['minlength']) {
            <mat-error>用户名至少需要3个字符</mat-error>
          } @else if (registerForm.get('username')?.errors?.['maxlength']) {
            <mat-error>用户名不能超过20个字符</mat-error>
          }
        }
      </mat-form-field>

      <!-- 邮箱 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>邮箱</mat-label>
        <input matInput formControlName="email" type="email" autocomplete="email">
        @if (registerForm.get('email')?.invalid && registerForm.get('email')?.touched) {
          @if (registerForm.get('email')?.errors?.['required']) {
            <mat-error>请输入邮箱</mat-error>
          } @else if (registerForm.get('email')?.errors?.['email']) {
            <mat-error>请输入有效的邮箱地址</mat-error>
          }
        }
      </mat-form-field>

      <!-- 密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>密码</mat-label>
        <input matInput formControlName="password" type="password" autocomplete="new-password">
        @if (registerForm.get('password')?.invalid && registerForm.get('password')?.touched) {
          @if (registerForm.get('password')?.errors?.['required']) {
            <mat-error>请输入密码</mat-error>
          } @else if (registerForm.get('password')?.errors?.['minlength']) {
            <mat-error>密码至少需要6个字符</mat-error>
          }
        }
      </mat-form-field>

      <!-- 确认密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>确认密码</mat-label>
        <input matInput formControlName="confirmPassword" type="password" autocomplete="new-password">
        @if (registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) {
          @if (registerForm.get('confirmPassword')?.errors?.['required']) {
            <mat-error>请确认密码</mat-error>
          }
        }
        @if (registerForm.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched) {
          <mat-error>两次输入的密码不匹配</mat-error>
        }
      </mat-form-field>

      <!-- 同意条款 -->
      <div class="terms-checkbox">
        <mat-checkbox formControlName="agreeTerms" color="primary">
          我已阅读并同意
          <a href="/terms" target="_blank">用户协议</a>
          和
          <a href="/privacy" target="_blank">隐私政策</a>
        </mat-checkbox>
        @if (registerForm.get('agreeTerms')?.invalid && registerForm.get('agreeTerms')?.touched) {
          <mat-error class="terms-error">必须同意用户协议才能注册</mat-error>
        }
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <button type="button" mat-button onclick="window.location.href='/login'">返回登录</button>
        <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
          @if (isSubmitting) {
            <mat-spinner diameter="20"></mat-spinner>
            <span>注册中...</span>
          } @else {
            <span>注册</span>
          }
        </button>
      </div>
    </form>
  </div>
</div>
