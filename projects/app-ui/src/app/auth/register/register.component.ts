import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {Router} from '@angular/router';
import {AuthService} from '../../core/services/auth.service';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss',
})
export class RegisterComponent {
  // 注册表单
  registerForm: FormGroup;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  constructor(
      private router: Router,
      private formBuilder: FormBuilder,
      private authService: AuthService,
  ) {
    // 初始化注册表单
    this.registerForm = this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      agreeTerms: [false, [Validators.requiredTrue]],
    }, {
      validators: this.passwordMatchValidator,
    });
  }

  /**
   * 密码匹配验证器
   */
  passwordMatchValidator(group: FormGroup): { [key: string]: boolean } | null {
    const password = group.get('password')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;

    return password === confirmPassword ? null : {passwordMismatch: true};
  }

  /**
   * 提交注册表单
   */
  onSubmit(): void {
    // 如果表单无效，则标记所有控件为已触摸，以显示错误信息
    if (this.registerForm.invalid) {
      this.registerForm.markAllAsTouched();
      return;
    }

    // 设置为正在提交状态
    this.isSubmitting = true;
    this.errorMessage = '';

    // 调用认证服务的注册方法
    this.authService.register(this.registerForm.value).subscribe({
      next: () => {
        // 注册成功，跳转到首页
        this.router.navigate(['/']);
      },
      error: (error) => {
        // 注册失败，显示错误消息
        this.isSubmitting = false;
        this.errorMessage = error.error?.error || '注册失败，请稍后再试';
      },
    });
  }
}
