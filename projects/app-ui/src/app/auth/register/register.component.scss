.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 16px;
  min-height: calc(100vh - 64px); // 减去顶部导航栏高度
}

.register-card {
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: var(--shadow-elevation-2);
  padding: 32px;
  width: 100%;
  max-width: 480px;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;

  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 500;
    }

    mat-icon {
      font-size: 28px;
      color: var(--primary-color);
    }
  }

  .header-subtitle {
    margin: 0;
    color: var(--text-secondary-color);
    font-size: 16px;
  }
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  width: 100%;
}

.error-message {
  background-color: var(--warn-lighter-color);
  color: var(--warn-color);
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
}

.terms-checkbox {
  margin: 8px 0;

  a {
    color: var(--primary-color);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .terms-error {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--warn-color);
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;

  button[type="submit"] {
    min-width: 80px;
    display: flex;
    align-items: center;
    gap: 8px;

    mat-spinner {
      margin-right: 8px;
    }
  }
}

// 响应式调整
@media (max-width: 599px) {
  .register-card {
    padding: 24px 16px;
  }

  .card-header {
    margin-bottom: 24px;

    .header-title {
      h1 {
        font-size: 24px;
      }

      mat-icon {
        font-size: 24px;
      }
    }
  }
}
