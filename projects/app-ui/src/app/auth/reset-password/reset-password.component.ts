import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {Router} from '@angular/router';
import {AuthService} from '../../core/services/auth.service';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss',
})
export class ResetPasswordComponent {
  // 重置密码表单
  resetForm: FormGroup;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  // 是否已提交成功
  isSuccess = false;

  constructor(
      private router: Router,
      private formBuilder: FormBuilder,
      private authService: AuthService,
  ) {
    // 初始化重置密码表单
    this.resetForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }

  /**
   * 提交重置密码表单
   */
  onSubmit(): void {
    // 如果表单无效，则标记所有控件为已触摸，以显示错误信息
    if (this.resetForm.invalid) {
      this.resetForm.markAllAsTouched();
      return;
    }

    // 设置为正在提交状态
    this.isSubmitting = true;
    this.errorMessage = '';

    // 调用认证服务的重置密码方法
    this.authService.resetPassword(this.resetForm.value).subscribe({
      next: () => {
        // 重置密码请求成功
        this.isSubmitting = false;
        this.isSuccess = true;
      },
      error: (error) => {
        // 重置密码请求失败，显示错误消息
        this.isSubmitting = false;
        this.errorMessage = error.error?.error || '重置密码请求失败，请稍后再试';
      },
    });
  }
}
