<div class="reset-password-dialog">
  <div class="dialog-header">
    <div class="dialog-title">
      <h2>找回密码</h2>
    </div>
    <p class="dialog-subtitle">输入您的邮箱，我们将向您发送重置密码的链接</p>
  </div>

  @if (!isSuccess) {
    <form [formGroup]="resetForm" (ngSubmit)="onSubmit()" class="reset-form">
      <!-- 错误消息 -->
      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }

      <!-- 邮箱 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>邮箱</mat-label>
        <input matInput formControlName="email" type="email" autocomplete="email">
        @if (resetForm.get('email')?.invalid && resetForm.get('email')?.touched) {
          @if (resetForm.get('email')?.errors?.['required']) {
            <mat-error>请输入邮箱</mat-error>
          } @else if (resetForm.get('email')?.errors?.['email']) {
            <mat-error>请输入有效的邮箱地址</mat-error>
          }
        }
      </mat-form-field>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <button type="button" mat-button (click)="goToLogin()">返回登录</button>
        @if (isSubmitting) {
          <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
            <mat-spinner diameter="20"></mat-spinner>
            <span>提交中...</span>
          </button>
        } @else {
          <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
            <mat-icon matButtonIcon svgIcon="lock-reset"></mat-icon>
            <span>发送重置链接</span>
          </button>
        }
      </div>
    </form>
  } @else {
    <!-- 成功消息 -->
    <div class="success-message">
      <p>重置密码链接已发送到您的邮箱，请查收并按照邮件中的指示进行操作。</p>
      <p>如果您没有收到邮件，请检查垃圾邮件文件夹，或者稍后再试。</p>
      <div class="success-actions">
        <button mat-raised-button color="primary" (click)="goToLogin()">返回登录</button>
      </div>
    </div>
  }
</div>
