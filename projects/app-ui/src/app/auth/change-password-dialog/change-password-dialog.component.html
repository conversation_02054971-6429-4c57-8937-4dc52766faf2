<div class="change-password-dialog">
  <div class="dialog-header">
    <div class="dialog-title">
      <h2>修改密码</h2>
    </div>
    <p class="dialog-subtitle">请输入您的当前密码和新密码</p>
  </div>

  @if (!isSuccess) {
    <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()" class="change-password-form">
      <!-- 错误消息 -->
      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }

      <!-- 当前密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>当前密码</mat-label>
        <input matInput formControlName="currentPassword" type="password" autocomplete="current-password">
        @if (changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched) {
          <mat-error>请输入当前密码</mat-error>
        }
      </mat-form-field>

      <!-- 新密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>新密码</mat-label>
        <input matInput formControlName="newPassword" type="password" autocomplete="new-password">
        @if (changePasswordForm.get('newPassword')?.invalid && changePasswordForm.get('newPassword')?.touched) {
          @if (changePasswordForm.get('newPassword')?.errors?.['required']) {
            <mat-error>请输入新密码</mat-error>
          } @else if (changePasswordForm.get('newPassword')?.errors?.['minlength']) {
            <mat-error>密码至少需要6个字符</mat-error>
          }
        }
      </mat-form-field>

      <!-- 确认新密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>确认新密码</mat-label>
        <input matInput formControlName="confirmPassword" type="password" autocomplete="new-password">
        @if (changePasswordForm.get('confirmPassword')?.hasError('required')) {
          <mat-error>请确认新密码</mat-error>
        } @else if (changePasswordForm.get('confirmPassword')?.hasError('passwordMismatch')) {
          <mat-error>两次输入的密码不一致</mat-error>
        }
      </mat-form-field>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <button type="button" mat-button (click)="onCancel()">取消</button>
        @if (isSubmitting) {
          <button type="submit" mat-raised-button color="primary" [disabled]="true">
            <mat-spinner diameter="20"></mat-spinner>
            <span>提交中...</span>
          </button>
        } @else {
          <button type="submit" mat-raised-button color="primary" [disabled]="changePasswordForm.invalid">
            <mat-icon matButtonIcon svgIcon="lock"></mat-icon>
            <span>修改密码</span>
          </button>
        }
      </div>
    </form>
  } @else {
    <!-- 成功消息 -->
    <div class="success-message">
      <p>密码修改成功！</p>
      <p>请使用新密码登录系统。</p>
      <div class="success-actions">
        <button mat-raised-button color="primary" (click)="onClose()">确定</button>
      </div>
    </div>
  }
</div>
