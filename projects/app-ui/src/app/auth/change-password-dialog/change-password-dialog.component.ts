import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {AuthService} from '../../core/services/auth.service';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-change-password-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './change-password-dialog.component.html',
  styleUrl: './change-password-dialog.component.scss',
  host: {
    class: 'change-password-dialog-host',
  },
})
export class ChangePasswordDialogComponent {
  // 修改密码表单
  changePasswordForm: FormGroup;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  // 是否已提交成功
  isSuccess = false;

  constructor(
      private dialogRef: MatDialogRef<ChangePasswordDialogComponent>,
      private formBuilder: FormBuilder,
      private authService: AuthService,
  ) {
    // 初始化修改密码表单
    this.changePasswordForm = this.formBuilder.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
    });

    // 添加值变化监听，以便在输入时验证密码匹配
    this.changePasswordForm.get('confirmPassword')?.valueChanges.subscribe(() => {
      this.validateForm();
    });

    this.changePasswordForm.get('newPassword')?.valueChanges.subscribe(() => {
      // 只有当确认密码字段已经有值时才验证
      if (this.changePasswordForm.get('confirmPassword')?.value) {
        this.validateForm();
      }
    });
  }

  /**
   * 提交前检查表单是否有效
   * @returns 表单是否有效
   */
  private validateForm(): boolean {
    // 标记所有控件为已触摸，以显示错误信息
    this.changePasswordForm.markAllAsTouched();

    // 手动触发一次密码匹配验证
    const newPassword = this.changePasswordForm.get('newPassword')?.value;
    const confirmPassword = this.changePasswordForm.get('confirmPassword')?.value;

    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      this.changePasswordForm.get('confirmPassword')?.setErrors({
        passwordMismatch: true,
      });
      return false;
    }

    return this.changePasswordForm.valid;
  }

  /**
   * 提交修改密码表单
   */
  onSubmit(): void {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    // 设置为正在提交状态
    this.isSubmitting = true;
    this.errorMessage = '';

    // 调用认证服务的修改密码方法
    this.authService.changePassword(this.changePasswordForm.value).subscribe({
      next: () => {
        // 修改密码成功
        this.isSubmitting = false;
        this.isSuccess = true;
      },
      error: (error) => {
        // 修改密码失败，显示错误消息
        this.isSubmitting = false;
        this.errorMessage = error.error?.error || '修改密码失败，请稍后再试';
      },
    });
  }

  /**
   * 关闭对话框
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * 关闭对话框并返回成功
   */
  onClose(): void {
    this.dialogRef.close(true);
  }
}
