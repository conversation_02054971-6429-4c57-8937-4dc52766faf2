import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {Router} from '@angular/router';
import {AuthService} from '../../core/services/auth.service';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  // 登录表单
  loginForm: FormGroup;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  constructor(
      private router: Router,
      private formBuilder: FormBuilder,
      private authService: AuthService,
  ) {
    // 获取保存的用户名和记住登录状态
    const savedUsername = this.authService.getSavedUsername();
    const rememberMe = this.authService.isRememberMe();

    // 初始化登录表单
    this.loginForm = this.formBuilder.group({
      username: [savedUsername, [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [rememberMe],
    });
  }

  /**
   * 提交登录表单
   */
  onSubmit(): void {
    // 如果表单无效，则标记所有控件为已触摸，以显示错误信息
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    // 设置为正在提交状态
    this.isSubmitting = true;
    this.errorMessage = '';

    // 调用认证服务的登录方法
    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        // 登录成功，跳转到首页
        this.router.navigate(['/']);
      },
      error: (error) => {
        // 登录失败，显示错误消息
        this.isSubmitting = false;
        this.errorMessage = error.error?.error || '登录失败，请检查用户名和密码';
      },
    });
  }
}
