<div class="login-container">
  <div class="login-card">
    <div class="card-header">
      <div class="header-title">
        <mat-icon svgIcon="login"></mat-icon>
        <h1>登录</h1>
      </div>
      <p class="header-subtitle">登录您的账号以访问所有功能</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- 错误消息 -->
      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }

      <!-- 用户名/邮箱 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>用户名或邮箱</mat-label>
        <input matInput formControlName="username" type="text" autocomplete="username">
        @if (loginForm.get('username')?.invalid && loginForm.get('username')?.touched) {
          <mat-error>请输入用户名或邮箱</mat-error>
        }
      </mat-form-field>

      <!-- 密码 -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>密码</mat-label>
        <input matInput formControlName="password" type="password" autocomplete="current-password">
        @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
          <mat-error>请输入密码</mat-error>
        }
      </mat-form-field>

      <!-- 记住我 -->
      <div class="remember-me">
        <mat-checkbox formControlName="rememberMe" color="primary">记住我</mat-checkbox>
        <a href="/reset-password" class="forgot-password">忘记密码？</a>
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        @if (isSubmitting) {
          <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
            <mat-spinner diameter="20"></mat-spinner>
            <span>登录中...</span>
          </button>
        } @else {
          <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
            <span>登录</span>
          </button>
        }
      </div>
    </form>

    <!-- 注册链接 -->
    <div class="register-link">
      <span>还没有账号？</span>
      <a href="/register">立即注册</a>
    </div>
  </div>
</div>
