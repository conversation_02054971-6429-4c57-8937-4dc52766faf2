<div class="login-dialog">
  <div class="dialog-header">
    <div class="dialog-title">
      <h2>登录</h2>
    </div>
    <p class="dialog-subtitle">请登录以继续访问</p>
  </div>

  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
    <!-- 错误消息 -->
    @if (errorMessage) {
      <div class="error-message">
        {{ errorMessage }}
      </div>
    }

    <!-- 用户名/邮箱 -->
    <mat-form-field appearance="outline" class="form-field">
      <mat-label>用户名或邮箱</mat-label>
      <input matInput formControlName="username" type="text" autocomplete="username">
      @if (loginForm.get('username')?.invalid && loginForm.get('username')?.touched) {
        <mat-error>请输入用户名或邮箱</mat-error>
      }
    </mat-form-field>

    <!-- 密码 -->
    <mat-form-field appearance="outline" class="form-field">
      <mat-label>密码</mat-label>
      <input matInput formControlName="password" type="password" autocomplete="current-password">
      @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
        <mat-error>请输入密码</mat-error>
      }
    </mat-form-field>

    <!-- 记住我 -->
    <div class="remember-me">
      <mat-checkbox formControlName="rememberMe" color="primary">记住我</mat-checkbox>
      <a href="javascript:void(0)" (click)="goToResetPassword()" class="forgot-password">忘记密码？</a>
    </div>

    <!-- 提交按钮 -->
    <div class="form-actions">
      <button type="button" mat-button (click)="onCancel()">取消</button>
      @if (isSubmitting) {
        <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting" class="submit-button">
          <div class="button-content">
            <mat-spinner diameter="20"></mat-spinner>
            <span>登录中...</span>
          </div>
        </button>
      } @else {
        <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting" class="submit-button">
          <div class="button-content">
            <mat-icon matButtonIcon svgIcon="login"></mat-icon>
            <span>登录</span>
          </div>
        </button>
      }
    </div>
  </form>

  <!-- 注册链接 -->
  <div class="register-link">
    <span>还没有账号？</span>
    <a href="javascript:void(0)" (click)="goToRegister()">立即注册</a>
  </div>
</div>
