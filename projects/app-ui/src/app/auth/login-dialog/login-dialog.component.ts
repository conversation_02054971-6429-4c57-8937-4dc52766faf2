import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatDialog, MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {AuthService} from '../../core/services/auth.service';
import {RegisterDialogComponent} from '../register-dialog/register-dialog.component';
import {ResetPasswordDialogComponent} from '../reset-password-dialog/reset-password-dialog.component';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-login-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './login-dialog.component.html',
  styleUrl: './login-dialog.component.scss',
  host: {
    class: 'login-dialog-host',
  },
})
export class LoginDialogComponent {
  // 登录表单
  loginForm: FormGroup;

  // 是否正在提交
  isSubmitting = false;

  // 错误消息
  errorMessage = '';

  constructor(
      private dialogRef: MatDialogRef<LoginDialogComponent>,
      private formBuilder: FormBuilder,
      private authService: AuthService,
      private dialog: MatDialog,
  ) {
    // 获取保存的用户名和记住登录状态
    const savedUsername = this.authService.getSavedUsername();
    const rememberMe = this.authService.isRememberMe();

    // 初始化登录表单
    this.loginForm = this.formBuilder.group({
      username: [savedUsername, [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [rememberMe],
    });
  }

  /**
   * 提交登录表单
   */
  onSubmit(): void {
    // 如果表单无效，则标记所有控件为已触摸，以显示错误信息
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    // 设置为正在提交状态
    this.isSubmitting = true;
    this.errorMessage = '';

    // 调用认证服务的登录方法
    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        // 登录成功，关闭对话框并返回true
        this.dialogRef.close(true);
      },
      error: (error) => {
        // 登录失败，显示错误消息
        this.isSubmitting = false;
        this.errorMessage = error.error?.error || '登录失败，请检查用户名和密码';
      },
    });
  }

  /**
   * 关闭对话框
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * 打开注册对话框
   */
  goToRegister(): void {
    // 关闭当前对话框
    this.dialogRef.close();

    // 打开注册对话框
    const dialogRef = this.dialog.open(RegisterDialogComponent, {
      width: '400px',
      maxWidth: '95vw',
    });

    // 处理注册对话框关闭事件
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'login') {
        // 如果用户点击了"返回登录"，则重新打开登录对话框
        this.dialog.open(LoginDialogComponent, {
          width: '400px',
          maxWidth: '95vw',
        });
      }
    });
  }

  /**
   * 打开找回密码对话框
   */
  goToResetPassword(): void {
    // 关闭当前对话框
    this.dialogRef.close();

    // 打开找回密码对话框
    const dialogRef = this.dialog.open(ResetPasswordDialogComponent, {
      width: '400px',
      maxWidth: '95vw',
    });

    // 处理找回密码对话框关闭事件
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'login') {
        // 如果用户点击了"返回登录"，则重新打开登录对话框
        this.dialog.open(LoginDialogComponent, {
          width: '400px',
          maxWidth: '95vw',
        });
      }
    });
  }
}
