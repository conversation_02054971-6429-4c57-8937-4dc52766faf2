:host {
  display: block;
  height: 100%;
}

.settings-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
  height: auto;
  overflow: hidden; /* 防止内容溢出导致滚动条 */
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

h2 {
  margin: var(--spacing-md) 0;
  color: var(--text-primary-color);
  font-size: var(--font-size-lg);
}

.tab-content {
  padding: var(--spacing-md) 0;
  overflow: hidden; /* 防止内容溢出导致滚动条 */
}

/* 确保标签内容不会导致滚动条 */
::ng-deep .mat-mdc-tab-body-wrapper {
  overflow: hidden;
}

/* 移除mat-tab-body上的滚动条 */
::ng-deep .mat-mdc-tab-body {
  overflow-y: hidden !important;
}

::ng-deep .mat-mdc-tab-body-content {
  overflow: hidden !important;
}

.settings-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.settings-actions {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;

  button {
    .mat-icon {
      margin-right: var(--spacing-xs);
    }
  }
}

.settings-list {
  .settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;

    .settings-item-info {
      flex: 1;

      h3 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-md);
        color: var(--text-primary-color);
      }

      p {
        margin: 0;
        color: var(--text-secondary-color);
        font-size: var(--font-size-sm);
      }
    }

    mat-form-field {
      width: 200px;
    }
  }

  mat-divider {
    margin: 0;
  }
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-lg);

  button {
    min-width: 120px;

    mat-spinner {
      display: inline-block;
      margin-right: var(--spacing-xs);
    }

    .button-text {
      vertical-align: middle;
    }

    mat-icon {
      margin-right: var(--spacing-xs);
    }
  }
}

/* 确保卡片内容不会导致滚动条 */
::ng-deep .mat-mdc-card-content {
  overflow: visible;
}

@media (max-width: 768px) {
  .settings-form {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }

  .settings-list {
    .settings-item {
      flex-direction: column;
      align-items: flex-start;

      .settings-item-info {
        margin-bottom: var(--spacing-sm);
      }

      mat-form-field, mat-slide-toggle {
        align-self: flex-start;
        width: 100%;
      }
    }
  }
}
