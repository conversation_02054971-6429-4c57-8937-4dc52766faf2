import {Component, OnInit, ViewChild} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatCardModule} from '@angular/material/card';
import {MatTabGroup, MatTabsModule} from '@angular/material/tabs';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatSelectModule} from '@angular/material/select';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {ActivatedRoute} from '@angular/router';
import {SettingService} from '../../api/setting.service';
import {Settings, SettingsResponse, UpdateSettingsResponse} from '../../core/models/setting.model';
import {ChangePasswordDialogComponent} from '../../auth/change-password-dialog/change-password-dialog.component';
import {TwoFactorAuthDialogComponent} from '../../auth/two-factor-auth-dialog/two-factor-auth-dialog.component';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatTabsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss',
})
export class SettingsComponent implements OnInit {
  @ViewChild(MatTabGroup) tabGroup!: MatTabGroup;

  // 当前选中的标签索引
  selectedTabIndex = 0;

  // 设置表单
  settingsForm!: FormGroup;

  // 加载状态
  loading = true;

  // 保存状态
  saving = false;

  // 错误信息
  error: string | null = null;

  // 主题选项
  themeOptions = [
    {value: 'light', label: '浅色主题'},
    {value: 'dark', label: '深色主题'},
    {value: 'system', label: '跟随系统'},
  ];

  // 字体大小选项
  fontSizeOptions = [
    {value: 'small', label: '小'},
    {value: 'medium', label: '中'},
    {value: 'large', label: '大'},
  ];

  // 隐私选项
  privacyOptions = [
    {value: 'public', label: '公开'},
    {value: 'friends', label: '仅好友可见'},
    {value: 'private', label: '私密'},
  ];

  constructor(
      private settingService: SettingService,
      private snackBar: MatSnackBar,
      private dialog: MatDialog,
      private route: ActivatedRoute,
      private fb: FormBuilder,
  ) {
  }

  ngOnInit(): void {
    this.initForm();
    this.loadSettingsData();

    // 从URL参数中获取标签信息
    this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        // 根据tab参数设置选中的标签
        switch (params['tab']) {
          case 'account':
            this.selectedTabIndex = 0;
            break;
          case 'notifications':
            this.selectedTabIndex = 1;
            break;
          case 'privacy':
            this.selectedTabIndex = 2;
            break;
          case 'appearance':
            this.selectedTabIndex = 3;
            break;
          default:
            this.selectedTabIndex = 0;
        }
      }
    });
  }

  /**
   * 初始化表单
   */
  initForm(): void {
    this.settingsForm = this.fb.group({
      account: this.fb.group({
        email: ['', [Validators.required, Validators.email]],
        phone: [''],
        language: ['zh_CN', Validators.required],
        timezone: ['Asia/Shanghai', Validators.required],
      }),
      notifications: this.fb.group({
        emailNotifications: [true],
        pushNotifications: [true],
        activitySummary: [true],
        marketingEmails: [false],
      }),
      privacy: this.fb.group({
        profileVisibility: ['public', Validators.required],
        showOnlineStatus: [true],
        allowTagging: [true],
        allowDataCollection: [true],
      }),
      appearance: this.fb.group({
        theme: ['light', Validators.required],
        fontSize: ['medium', Validators.required],
        reducedMotion: [false],
        highContrast: [false],
      }),
    });
  }

  /**
   * 加载设置数据
   */
  loadSettingsData(): void {
    this.loading = true;
    this.error = null;

    this.settingService.getSettings().subscribe({
      next: (response: SettingsResponse) => {
        if (response && response.settings) {
          this.settingsForm.patchValue(response.settings);
        } else {
          // 可以选择设置一个默认值或显示错误
          console.warn('获取到的设置数据格式不正确或为空', response);
          this.error = '获取设置数据格式不正确';
        }
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取设置数据失败:', err);
        this.error = '获取设置数据失败，请稍后再试';
        this.loading = false;
      },
    });
  }

  // 保存设置
  saveSettings(): void {
    if (this.settingsForm.invalid) {
      this.snackBar.open('请检查表单输入是否正确', '关闭', {
        duration: 3000,
      });
      // 标记所有控件为已触摸，以显示错误信息
      this.settingsForm.markAllAsTouched();
      return;
    }

    this.saving = true;
    const settingsData = this.settingsForm.value as Settings;

    this.settingService.updateSettings(settingsData).subscribe({
      next: (response: UpdateSettingsResponse) => {
        this.saving = false;
        if (response && response.settings) {
          this.settingsForm.patchValue(response.settings); // 使用后端返回的最新数据更新表单
        }
        this.snackBar.open(response.message || '设置已保存', '关闭', {
          duration: 3000,
        });
      },
      error: (err: Error) => {
        console.error('保存设置失败:', err);
        this.saving = false;
        this.snackBar.open('保存设置失败，请稍后再试', '关闭', {
          duration: 3000,
        });
      },
    });
  }

  /**
   * 打开修改密码对话框
   */
  openChangePasswordDialog(): void {
    // 获取窗口宽度
    const windowWidth = window.innerWidth;

    // 根据窗口宽度设置对话框宽度
    let dialogWidth = '400px'; // 默认宽度

    if (windowWidth < 480) {
      // 在小屏幕上使用更窄的宽度或百分比
      dialogWidth = '95%';
    }

    this.dialog.open(ChangePasswordDialogComponent, {
      width: dialogWidth,
      maxWidth: '95vw', // 最大宽度不超过视口宽度的95%
    });
  }

  /**
   * 打开两步认证对话框
   */
  openTwoFactorAuthDialog(): void {
    // 获取窗口宽度
    const windowWidth = window.innerWidth;

    // 根据窗口宽度设置对话框宽度
    let dialogWidth = '500px'; // 默认宽度

    if (windowWidth < 480) {
      // 在小屏幕上使用更窄的宽度或百分比
      dialogWidth = '95%';
    }

    this.dialog.open(TwoFactorAuthDialogComponent, {
      width: dialogWidth,
      maxWidth: '95vw', // 最大宽度不超过视口宽度的95%
    });
  }
}
