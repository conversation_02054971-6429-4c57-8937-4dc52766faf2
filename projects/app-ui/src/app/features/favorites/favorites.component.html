<div class="favorites-container">
  <h1>我的收藏</h1>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadFavoritesData()">重试</button>
    </div>
  }

  <!-- 收藏内容 -->
  @if (!loading && !error) {
    <div class="favorites-list">
      @if (favorites.length > 0) {
        @for (item of favorites; track item.id) {
          <mat-card appearance="outlined" class="favorite-item">
            <mat-card-content>
              <div class="favorite-header">
                <div class="favorite-type" [ngClass]="item.type">
                  @if (item.type === 'article') {
                    <mat-icon svgIcon="article"></mat-icon>
                    <span>文章</span>
                  } @else if (item.type === 'video') {
                    <mat-icon svgIcon="videocam"></mat-icon>
                    <span>视频</span>
                  } @else {
                    <mat-icon svgIcon="bookmark"></mat-icon>
                    <span>收藏</span>
                  }
                </div>
                <button mat-icon-button [matMenuTriggerFor]="itemMenu" aria-label="操作菜单">
                  <mat-icon svgIcon="more-vert" matButtonIcon></mat-icon>
                </button>
                <mat-menu #itemMenu="matMenu">
                  <button mat-menu-item>
                    <mat-icon svgIcon="open-in-new" matButtonIcon></mat-icon>
                    <span>查看详情</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon svgIcon="share" matButtonIcon></mat-icon>
                    <span>分享</span>
                  </button>
                  <button mat-menu-item (click)="removeFavorite(item.id)">
                    <mat-icon svgIcon="delete" matButtonIcon></mat-icon>
                    <span>取消收藏</span>
                  </button>
                </mat-menu>
              </div>

              <h2 class="favorite-title">{{ item.title }}</h2>
              <p class="favorite-description">{{ item.description }}</p>

              <div class="favorite-meta">
                <div class="meta-info">
                  <span class="meta-author">{{ item.author }}</span>
                  <span class="meta-date">{{ item.date }}</span>
                </div>
                <div class="favorite-tags">
                  @for (tag of item.tags; track tag) {
                    <mat-chip-option selected>{{ tag }}</mat-chip-option>
                  }
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        }
      } @else {
        <div class="empty-state">
          <mat-icon svgIcon="bookmark-border" matButtonIcon></mat-icon>
          <p>暂无收藏内容</p>
          <button mat-raised-button color="primary">浏览内容</button>
        </div>
      }
    </div>
  }
</div>
