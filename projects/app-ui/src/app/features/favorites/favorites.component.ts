import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatChipsModule} from '@angular/material/chips';
import {MatMenuModule} from '@angular/material/menu';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {FavoriteService} from '../../api/favorite.service';
import {DeleteFavoriteResponse, Favorite, FavoriteResponse} from '../../core/models/favorite.model';

@Component({
  selector: 'app-favorites',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
  ],
  templateUrl: './favorites.component.html',
  styleUrl: './favorites.component.scss',
})
export class FavoritesComponent implements OnInit {
  // 收藏数据
  favorites: Favorite[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  constructor(
      private favoriteService: FavoriteService,
      private snackBar: MatSnackBar,
  ) {
  }

  ngOnInit(): void {
    this.loadFavoritesData();
  }

  /**
   * 加载收藏数据
   */
  loadFavoritesData(): void {
    this.loading = true;
    this.error = null;

    this.favoriteService.getFavorites().subscribe({
      next: (data: FavoriteResponse) => {
        this.favorites = data.favorites || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取收藏数据失败:', err);
        this.error = '获取收藏数据失败，请稍后再试';
        this.loading = false;
      },
    });
  }

  // 移除收藏
  removeFavorite(id: number): void {
    this.favoriteService.deleteFavorite(id).subscribe({
      next: (_: DeleteFavoriteResponse) => {
        this.favorites = this.favorites.filter(item => item.id !== id);
        this.snackBar.open('收藏已删除', '关闭', {
          duration: 3000,
        });
      },
      error: (err: Error) => {
        console.error('删除收藏失败:', err);
        this.snackBar.open('删除收藏失败，请稍后再试', '关闭', {
          duration: 3000,
        });
      },
    });
  }
}
