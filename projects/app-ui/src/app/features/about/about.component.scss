:host {
  display: block;
}

.about-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.about-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  h1 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
  }

  .subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary-color);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--text-secondary-color);
  padding: var(--spacing-xl) 0;
}

.about-section {
  margin-bottom: var(--spacing-xl);

  h2 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary-color);
    font-size: var(--font-size-xl);
  }
}

.company-info {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: var(--spacing-lg);

  .company-text {
    h3 {
      margin: var(--spacing-md) 0 var(--spacing-sm);
      color: var(--primary-color);
    }

    p {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-secondary-color);
      line-height: 1.6;
    }
  }

  .company-image {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--border-radius-md);
    }
  }
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--spacing-md);
}

.team-card {
  text-align: center;

  .member-avatar {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-md);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
      border: 3px solid var(--primary-light-color);
    }
  }

  h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary-color);
  }

  .member-title {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
  }

  .member-bio {
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);
    line-height: 1.5;
  }
}

.timeline {
  position: relative;
  padding: var(--spacing-md) 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--primary-light-color);
  }

  .timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: var(--spacing-lg);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timeline-badge {
    position: absolute;
    left: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }

  .timeline-content {
    h3 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary-color);
    }

    p {
      margin: 0;
      color: var(--text-secondary-color);
    }
  }
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.value-card {
  text-align: center;

  .mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
  }

  h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary-color);
  }

  p {
    margin: 0;
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);
    line-height: 1.5;
  }
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: flex-start;

  .mat-icon {
    color: var(--primary-color);
    margin-right: var(--spacing-sm);
  }

  .contact-text {
    h3 {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--font-size-md);
      color: var(--text-primary-color);
    }

    p {
      margin: 0;
      color: var(--text-secondary-color);
    }
  }
}

.contact-actions {
  display: flex;
  justify-content: center;

  button {
    .mat-icon {
      margin-right: var(--spacing-xs);
    }
  }
}

@media (max-width: 768px) {
  .company-info {
    grid-template-columns: 1fr;

    .company-image {
      order: -1;
      margin-bottom: var(--spacing-md);
    }
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
