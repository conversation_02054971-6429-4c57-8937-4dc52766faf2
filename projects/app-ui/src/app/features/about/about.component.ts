import {Component, OnInit} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {RouterLink} from '@angular/router';

// 定义 AboutInfoDto 接口，因为 about.model.ts 将被删除
interface AboutInfoDto {
  title: string;
  description: string;
  icon?: string;
  imageUrl?: string;
  details?: string[];
}

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    RouterLink,
    NgOptimizedImage,
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss',
})
export class AboutComponent {
  // 硬编码的静态数据
  company: AboutInfoDto[] = [
    {
      title: '北京智座科技发展中心（工作室）',
      description: '成立于2023年10月，旨在帮助小微企业导入 AI 能力。',
      icon: 'rocket_launch', // 示例图标
    }
  ];

  team: AboutInfoDto[] = [
    {
      title: '汪志成',
      description: '一位有着27年开发经验的资深工程师，曾在 ThoughtWorks 担任专家级咨询师，有社区荣誉 Google Developer Expert。',
      imageUrl: 'assets/photo.png', // 示例图片
    }
  ];

  milestones: AboutInfoDto[] = [
    {
      title: '2023 - 公司成立',
      description: '北京智座科技发展中心（工作室）成立，旨在帮助小微企业导入 AI 能力。',
      icon: 'flag',
    }
  ];

  constructor() {
  }
}
