<div class="about-container">
  <div class="about-header">
    <h1>关于我们</h1>
    <p class="subtitle">了解我们的故事、使命和团队</p>
  </div>

  <!-- 关于内容 -->
  <div class="about-content-wrapper"> <!-- 新增一个包装器，如果之前依赖 !loading && !error 来控制整体显示的话 -->
    @if (company && company.length > 0) {
      <div class="about-section">
        <mat-card appearance="outlined">
          <mat-card-content>
            <div class="company-info">
              <div class="company-text">
                <h2>{{ company[0].title || '公司简介' }}</h2>
                <p>{{ company[0].description || '欢迎了解我们公司。' }}</p>
                <!--
                Mission and Vision might need specific AboutInfoDto items with type='mission'/'vision'
                or be part of company[0]?.content. For now, they are combined into the main description.
              -->
            </div>
            <div class="company-image">
              <img ngSrc="/assets/wechat.png" alt="公司微信" height="233" width="228">
            </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    } @else {
      <div class="about-section">
        <p>公司信息正在加载中或暂不可用。</p>
      </div>
    }

    <div class="about-section">
      <h2>我们的团队</h2>
      @if (team.length === 0) {
        <p class="empty-state">暂无团队成员信息</p>
      } @else {
        <div class="team-grid">
          @for (item of team; track $index) {
            <mat-card appearance="outlined" class="team-card">
              <mat-card-content>
                <div class="member-avatar">
                  <img [ngSrc]="item.imageUrl || '/assets/default-avatar.svg'" alt="{{item.title}}" height="200"
                       width="200">
                </div>
                <h3>{{ item.title }}</h3>
                <!-- item.position 不再存在 -->
                <p class="member-bio">{{ item.description }}</p>
              </mat-card-content>
            </mat-card>
          }
        </div>
      }
    </div>

    <div class="about-section">
      <h2>发展历程</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          @if (milestones.length === 0) {
            <p class="empty-state">暂无发展历程信息</p>
          } @else {
            <div class="timeline">
              @for (item of milestones; track $index) {
                <div class="timeline-item">
                  <!-- item.dateValue 不再存在, item.title 现在包含年份信息 -->
                  <div class="timeline-badge">
                    @if(item.icon) {
                      <mat-icon>{{item.icon}}</mat-icon>
                    } @else {
                      <span>{{ item.title.substring(0, 4) }}</span> <!-- 尝试从标题提取年份 -->
                    }
                  </div>
                  <div class="timeline-content">
                    <h3>{{ item.title }}</h3>
                    <p>{{ item.description }}</p>
                  </div>
                </div>
              }
            </div>
          }
        </mat-card-content>
      </mat-card>
    </div>

    <div class="about-section">
      <h2>我们的价值观</h2>
      <div class="values-grid">
        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="lightbulb" matButtonIcon></mat-icon>
            <h3>创新</h3>
            <p>我们鼓励创新思维，不断探索新技术和解决方案。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="people" matButtonIcon></mat-icon>
            <h3>协作</h3>
            <p>我们相信团队协作的力量，共同创造卓越成果。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="verified-user" matButtonIcon></mat-icon>
            <h3>诚信</h3>
            <p>我们坚持诚信经营，赢得用户和合作伙伴的信任。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="trending-up" matButtonIcon></mat-icon>
            <h3>卓越</h3>
            <p>我们追求卓越品质，不断超越自我和用户期望。</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div class="about-section">
      <h2>联系我们</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          <div class="contact-grid">
            <div class="contact-item">
              <mat-icon svgIcon="location-on" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>地址</h3>
                <p>中国，北京市，中关村软件园</p> <!-- 硬编码地址 -->
              </div>
            </div>

            <div class="contact-item">
              <mat-icon svgIcon="email" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>邮箱</h3>
                <p>contact&#64;zhizuo.biz</p>
              </div>
            </div>

            <div class="contact-item">
              <mat-icon svgIcon="phone" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>电话</h3>
                <p>+86 10 1234 5678</p> <!-- 硬编码电话 -->
              </div>
            </div>

            <div class="contact-item">
              <mat-icon svgIcon="schedule" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>工作时间</h3>
                <p>周一至周五 9:00-18:00</p>
              </div>
            </div>
          </div>

          <div class="contact-actions">
            <button mat-raised-button color="primary" routerLink="/contact">
              <mat-icon matButtonIcon svgIcon="message"></mat-icon>
              联系我们
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div> <!-- 闭合 about-content-wrapper -->
</div>
