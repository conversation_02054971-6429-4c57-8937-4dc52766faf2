<div class="profile-container">
  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadProfileData()">重试</button>
    </div>
  }

  <!-- 个人资料内容 -->
  @if (!loading && !error && user) {
    <div class="profile-header">
      <mat-card appearance="outlined">
        <mat-card-content>
          <div class="profile-info">
            <div class="profile-avatar">
              <img [src]="user.avatar" alt="用户头像" onerror="this.src='assets/default-avatar.svg'">
            </div>
            <div class="profile-details">
              <h1>{{ user.name }}</h1>
              <p class="profile-bio">{{ user.bio }}</p>
              <div class="profile-meta">
                <div class="meta-item">
                  <mat-icon svgIcon="email"></mat-icon>
                  <span>{{ user.email }}</span>
                </div>
                <div class="meta-item">
                  <mat-icon svgIcon="phone"></mat-icon>
                  <span>{{ user.phone }}</span>
                </div>
                <div class="meta-item">
                  <mat-icon svgIcon="calendar"></mat-icon>
                  <span>加入于 {{ user.joinDate }}</span>
                </div>
              </div>
            </div>
            <div class="profile-actions">
              <button mat-raised-button color="primary" routerLink="/settings" [queryParams]="{tab: 'account'}">
                <mat-icon svgIcon="edit" matButtonIcon></mat-icon>
                编辑资料
              </button>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ user.stats.posts }}</div>
              <div class="stat-label">文章</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ user.stats.followers }}</div>
              <div class="stat-label">粉丝</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ user.stats.following }}</div>
              <div class="stat-label">关注</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div class="profile-content">
      <mat-tab-group animationDuration="0ms">
        <mat-tab label="活动">
          <div class="tab-content">
            @if (activities.length === 0) {
              <p class="empty-state">暂无活动</p>
            } @else {
              <div class="activity-list">
                @for (activity of activities; track activity.title) {
                  <div class="activity-item">
                    <div class="activity-icon">
                      <mat-icon svgIcon="history"></mat-icon>
                    </div>
                    <div class="activity-details">
                      <div class="activity-header">
                        <span class="activity-type">{{ activity.type }}</span>
                        <span class="activity-time">{{ activity.time }}</span>
                      </div>
                      <div class="activity-title">{{ activity.title }}</div>
                    </div>
                  </div>
                }
              </div>
            }
          </div>
        </mat-tab>
        <mat-tab label="文章">
          <div class="tab-content">
            <p class="empty-state">暂无文章</p>
          </div>
        </mat-tab>
        <mat-tab label="收藏">
          <div class="tab-content">
            <p class="empty-state">暂无收藏</p>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  }
</div>
