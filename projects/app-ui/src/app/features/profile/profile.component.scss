:host {
  display: block;
}

.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.profile-header {
  margin-bottom: var(--spacing-lg);
}

.profile-info {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.profile-avatar {
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light-color);
  }
}

.profile-details {
  h1 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary-color);
  }

  .profile-bio {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary-color);
  }

  .profile-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);

    .meta-item {
      display: flex;
      align-items: center;
      color: var(--text-secondary-color);

      .mat-icon {
        margin-right: var(--spacing-xs);
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-md) 0;

  .stat-item {
    text-align: center;

    .stat-value {
      font-size: var(--font-size-xl);
      font-weight: 500;
      color: var(--primary-color);
    }

    .stat-label {
      color: var(--text-secondary-color);
    }
  }
}

.profile-content {
  .tab-content {
    padding: var(--spacing-md) 0;
  }

  .activity-list {
    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: var(--spacing-md);
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);

      &:last-child {
        border-bottom: none;
      }

      .activity-icon {
        margin-right: var(--spacing-md);

        .mat-icon {
          color: var(--primary-color);
        }
      }

      .activity-details {
        flex: 1;

        .activity-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-xs);

          .activity-type {
            font-weight: 500;
          }

          .activity-time {
            color: var(--text-secondary-color);
            font-size: var(--font-size-sm);
          }
        }

        .activity-title {
          color: var(--text-primary-color);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    color: var(--text-secondary-color);
    padding: var(--spacing-xl) 0;
  }
}

@media (max-width: 768px) {
  .profile-info {
    grid-template-columns: 1fr;
    text-align: center;

    .profile-avatar {
      margin: 0 auto;
    }

    .profile-meta {
      justify-content: center;
    }

    .profile-actions {
      margin-top: var(--spacing-md);
    }
  }
}
