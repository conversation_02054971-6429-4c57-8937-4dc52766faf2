import {Component, inject, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatTabsModule} from '@angular/material/tabs';
import {MatButtonModule} from '@angular/material/button';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {RouterLink} from '@angular/router';
import {ProfileService} from '../../api/profile.service';
import {Activity, Profile, ProfileResponse} from '../../core/models/profile.model';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    RouterLink,
    MatIconModule,
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent implements OnInit {
  // 用户数据
  user: Profile | null = null;

  // 活动数据
  activities: Activity[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  // 使用 inject 函数注入服务
  private profileService = inject(ProfileService);

  ngOnInit(): void {
    this.loadProfileData();
  }

  /**
   * 加载个人资料数据
   */
  loadProfileData(): void {
    this.loading = true;
    this.error = null;

    this.profileService.getProfile().subscribe({
      next: (data: ProfileResponse) => {
        this.user = data.profile;
        this.activities = data.activities || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取个人资料失败:', err);
        this.error = '获取个人资料失败，请稍后再试';
        this.loading = false;
      },
    });
  }
}
