import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatCardModule} from '@angular/material/card';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatSelectModule} from '@angular/material/select';
import {MatButtonModule} from '@angular/material/button';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatSnackBar} from '@angular/material/snack-bar';
// import {AboutService} from '../../api/about.service'; // Removed
import {HelpService} from '../../api/help.service';
import {ContactService} from '../../api/contact.service'; // Corrected: ContactResponse removed from here
// import {CompanyInfo} from '../../core/models/about.model'; // Removed
import {ContactRequest, ContactResponse} from '../../core/models/contact.model'; // Corrected: ContactResponse imported from model
import {HelpResponse} from '../../core/models/help.model'; // Corrected: HelpResponse imported from model
import {MatIconModule} from '@angular/material/icon';

// Define CompanyInfo interface locally as about.model.ts is removed
interface CompanyInfo {
  name: string;
  founded?: string; // Optional as not all fields were used
  mission?: string; // Optional
  vision?: string;  // Optional
  address: string;
  email: string;
  phone?: string;   // Optional
  website?: string; // Optional
}

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss',
})
export class ContactComponent implements OnInit {
  // 联系表单
  contactForm!: FormGroup;

  // 表单提交状态
  submitting = false;
  submitted = false;
  submitSuccess = false;
  submitError: string | null = null;

  // 主题选项
  subjectOptions = [
    {value: 'general', label: '一般咨询'},
    {value: 'support', label: '技术支持'},
    {value: 'feedback', label: '产品反馈'},
    {value: 'partnership', label: '商务合作'},
    {value: 'other', label: '其他'},
  ];

  // 公司信息 (Hardcoded)
  companyInfo: CompanyInfo = {
    name: '北京智座科技发展中心（工作室）',
    address: '中国，北京市，通州区', // 地址保持不变，用户信息未提供
    email: 'contact&#64;zhizuo.biz', // Use HTML entity for @
    phone: '+86 17310398685',
    website: 'https://zhizuo.biz',
  };

  // 技术支持邮箱
  supportEmail = '<EMAIL>'; // 直接硬编码技术支持邮箱

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  // 联系方式
  contactInfo: any[] = [];

  constructor(
      private fb: FormBuilder,
      // private aboutService: AboutService, // Removed
      private helpService: HelpService,
      private contactService: ContactService,
      private snackBar: MatSnackBar,
  ) {
    // 初始化表单
    this.initForm();
  }

  ngOnInit(): void {
    this.loadData();
  }

  /**
   * 初始化表单
   */
  private initForm(): void {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(100)]],
      phone: ['', [Validators.maxLength(20)]],
      subject: ['', [Validators.required]],
      message: ['', [Validators.required, Validators.maxLength(1000)]],
    });
  }

  /**
   * 加载数据
   */
  loadData(): void {
    this.loading = true;
    this.error = null;

    // 公司信息已硬编码，直接使用
    this.contactInfo = [
      {
        icon: 'location',
        title: '地址',
        details: this.companyInfo.address,
      },
      {
        icon: 'email',
        title: '邮箱',
        details: this.companyInfo.email.replace('&#64;', '@'), // Display @ normally here
      },
      {
        icon: 'phone',
        title: '电话',
        details: this.companyInfo.phone,
      },
      {
        icon: 'schedule',
        title: '工作时间',
        details: '周一至周五 9:00-18:00',
      },
    ];

    // 加载技术支持邮箱
    this.loadSupportEmail();
  }

  /**
   * 加载技术支持邮箱
   */
  loadSupportEmail(): void {
    this.helpService.getHelp().subscribe({
      next: (data: HelpResponse) => { // Added HelpResponse type
        if (data.supportEmail) {
          this.supportEmail = data.supportEmail;
        }
        this.loading = false; // Should be set to false after all data loading attempts
      },
      error: (err: any) => { // Kept err as any for now, can be refined if Error type from service is known
        console.error('获取技术支持邮箱失败:', err);
        this.error = '获取技术支持信息失败，请稍后再试。'; // It's better to inform the user
        this.loading = false;
      },
    });
  }

  /**
   * 提交表单
   */
  submitForm(): void {
    this.submitted = true;

    // 表单验证失败
    if (this.contactForm.invalid) {
      return;
    }

    this.submitting = true;
    this.submitSuccess = false;
    this.submitError = null;

    // 准备提交数据
    const formData: ContactRequest = this.contactForm.value;

    // 提交到后端
    this.contactService.submitContact(formData).subscribe({
      next: (response: ContactResponse) => { // Added ContactResponse type
        this.submitting = false;
        this.submitSuccess = true;

        // 显示成功消息
        this.snackBar.open(response.message || '表单已提交，我们会尽快与您联系！', '关闭', {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
        });

        // 重置表单
        this.resetForm();
      },
      error: (error) => {
        this.submitting = false;
        this.submitError = error.error?.message || '提交失败，请稍后再试';

        // 显示错误消息
        this.snackBar.open(this.submitError || '提交失败', '关闭', {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
        });
      },
    });
  }

  /**
   * 重置表单
   */
  resetForm(): void {
    this.contactForm.reset();
    this.submitted = false;
    this.submitSuccess = false;
    this.submitError = null;
  }

  /**
   * 获取表单控件
   */
  get f() {
    return this.contactForm.controls;
  }
}
