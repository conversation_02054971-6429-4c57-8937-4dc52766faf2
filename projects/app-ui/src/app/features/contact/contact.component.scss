:host {
  display: block;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.contact-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  h1 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
  }

  .subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary-color);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
  }
}

.contact-content {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

h2 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary-color);
  font-size: var(--font-size-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);

  button {
    .mat-icon {
      margin-right: var(--spacing-xs);
    }
  }
}

// 提交状态样式
.submit-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;

  p {
    margin: 0;
  }
}

.submit-error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;

  p {
    margin: 0;
  }
}

// 提交按钮中的加载动画
button[type="submit"] {
  min-width: 100px;

  mat-spinner {
    display: inline-block;
    margin-right: var(--spacing-sm);
    vertical-align: middle;
  }
}

.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.contact-info-list {
  .contact-info-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md) 0;

    .info-icon {
      margin-right: var(--spacing-md);

      .mat-icon {
        color: var(--primary-color);
      }
    }

    .info-content {
      h3 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-md);
        color: var(--text-primary-color);
      }

      p {
        margin: 0;
        color: var(--text-secondary-color);
      }
    }
  }
}

.social-title {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  font-size: var(--font-size-md);
  color: var(--text-primary-color);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
    transition: background-color var(--transition-fast), box-shadow var(--transition-fast);

    &:hover {
      background-color: var(--primary-color);
      color: white;
      box-shadow: var(--shadow-md);
    }
  }
}

.map-card {
  .map-container {
    height: 200px;
    overflow: hidden;
    border-radius: var(--border-radius-md);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.wechat-card {
  .wechat-container {
    display: flex;
    justify-content: center;
    padding: var(--spacing-md) 0;

    img {
      width: 200px;
      height: 200px;
      object-fit: contain;
    }
  }
}

.contact-faq {
  h2 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
  }
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.faq-card {
  height: 100%;

  h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: var(--font-size-md);
  }

  p {
    margin: 0;
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);
    line-height: 1.5;
  }
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}
