:host {
  display: block;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

mat-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  mat-card-content {
    flex-grow: 1;
  }
}

@media (max-width: 599px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
