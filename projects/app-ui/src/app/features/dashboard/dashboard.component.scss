:host {
  display: block;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--text-secondary-color);
  padding: var(--spacing-md) 0;
}

h2 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);

  .icon-thumb {
    margin-bottom: var(--spacing-sm);
  }
}

.stat-content {
  text-align: center;

  h3 {
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-md);
    color: var(--text-secondary-color);
  }

  .stat-value {
    font-size: var(--font-size-xl);
    font-weight: 500;
    color: var(--text-primary-color);
  }
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
}

.activity-list {
  .activity-item {
    display: flex;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    &:last-child {
      border-bottom: none;
    }

    .activity-time {
      min-width: 80px;
      color: var(--text-secondary-color);
      font-size: var(--font-size-sm);
    }

    .activity-details {
      h4 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-md);
      }

      p {
        margin: 0;
        color: var(--text-secondary-color);
      }
    }
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);

  button {
    height: 48px;
    justify-content: flex-start;
    font-size: var(--font-size-md);
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}
