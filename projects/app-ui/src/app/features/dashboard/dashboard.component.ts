import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {RouterLink} from '@angular/router';
import {DashboardService} from '../../api/dashboard.service';
import {DashboardActivity, DashboardResponse, DashboardStat} from '../../core/models/dashboard.model';
import {MatIconModule} from '@angular/material/icon';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    RouterLink,
    MatIconModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  // 仪表盘数据
  dashboardStats: DashboardStat[] = [];

  // 最近活动
  recentActivities: DashboardActivity[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  constructor(private dashboardService: DashboardService) {
  }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  /**
   * 加载仪表盘数据
   */
  loadDashboardData(): void {
    this.loading = true;
    this.error = null;

    this.dashboardService.getDashboard().subscribe({
      next: (data: DashboardResponse) => {
        this.dashboardStats = data.stats || [];
        this.recentActivities = data.activities || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取仪表盘数据失败:', err);
        this.error = '获取仪表盘数据失败，请稍后再试';
        this.loading = false;
      },
    });
  }
}
