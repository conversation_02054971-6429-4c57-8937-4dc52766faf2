<div class="dashboard-container">
  <h1>控制台</h1>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadDashboardData()">重试</button>
    </div>
  }

  <!-- 仪表盘内容 -->
  @if (!loading && !error) {
    <div class="stats-grid">
      @for (stat of dashboardStats; track stat.title) {
        <mat-card appearance="outlined" class="stat-card">
          <div class="icon-thumb" [style.background-color]="stat.color">
            <mat-icon [svgIcon]="stat.icon"></mat-icon>
          </div>
          <div class="stat-content">
            <h3>{{ stat.title }}</h3>
            <div class="stat-value">{{ stat.value }}</div>
          </div>
        </mat-card>
      }
    </div>

    <div class="dashboard-content">
      <div class="dashboard-section">
        <h2>最近活动</h2>
        <mat-card appearance="outlined">
          <mat-card-content>
            @if (recentActivities.length === 0) {
              <p class="empty-state">暂无活动</p>
            } @else {
              <div class="activity-list">
                @for (activity of recentActivities; track activity.title) {
                  <div class="activity-item">
                    <div class="activity-time">{{ activity.time }}</div>
                    <div class="activity-details">
                      <h4>{{ activity.title }}</h4>
                      <p>{{ activity.description }}</p>
                    </div>
                  </div>
                }
              </div>
            }
          </mat-card-content>
        </mat-card>
      </div>

      <div class="dashboard-section">
        <h2>快速操作</h2>
        <div class="quick-actions">
          <button mat-raised-button color="primary" routerLink="/profile">
            <mat-icon svgIcon="person" matButtonIcon></mat-icon>
            个人中心
          </button>
          <button mat-raised-button color="accent" routerLink="/messages">
            <mat-icon svgIcon="message" matButtonIcon></mat-icon>
            消息中心
          </button>
          <button mat-raised-button color="warn" routerLink="/settings">
            <mat-icon svgIcon="settings" matButtonIcon></mat-icon>
            系统设置
          </button>
        </div>
      </div>
    </div>
  }
</div>
