import {Component, effect, EventEmitter, Output} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatButtonModule} from '@angular/material/button';
import {MatCardModule} from '@angular/material/card';
import {MatTooltipModule} from '@angular/material/tooltip';
import {ChatSuggestionService} from '../_services/chat-suggestion.service';
import {ChatSessionService} from '../_services/chat-session.service';
import {ChatMessageService} from '../_services/chat-message.service';
import {ChatMessageApi} from '../_services/chat-message-api.service';
import {ChatMessage} from '../_services/chat-message';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {MatIcon} from '@angular/material/icon';

export interface SuggestionTemplate {
  id: string;
  text: string;
  description: string;
  isReportTemplate?: boolean;
  icon?: string;
}

@Component({
  selector: 'app-chat-suggestion-templates',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatTooltipModule,
    MatIcon,
  ],
  templateUrl: './chat-suggestion-templates.component.html',
  styleUrl: './chat-suggestion-templates.component.scss',
})
export class ChatSuggestionTemplatesComponent {
  @Output() templateSelected = new EventEmitter<string>();

  templates: SuggestionTemplate[] = [];
  reportTemplates: SuggestionTemplate[] = [];
  isLoading = true;
  currentSessionId: string | undefined;
  currentMessageId: string | undefined;
  isCollapsed = true; // 折叠状态，默认折叠

  constructor(
      private suggestionService: ChatSuggestionService,
      private sessionService: ChatSessionService,
      protected messageService: ChatMessageService,
      private messageApi: ChatMessageApi,
  ) {
    // 监听会话变化，加载建议
    this.sessionService.selection$
        .pipe(takeUntilDestroyed())
        .subscribe((session) => {
          if (session) {
            this.currentSessionId = session.id;

            // 获取会话的所有消息
            this.messageApi
                .queryBySessionId(session.id)
                .subscribe((messages: ChatMessage[]) => {
                  if (messages && messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    this.currentMessageId = lastMessage.id;
                    this.loadSuggestions(session.id, lastMessage.id);
                  } else {
                    this.loadSuggestions(session.id);
                  }
                });
          } else {
            this.currentSessionId = undefined;
            this.currentMessageId = undefined;
            this.templates = [];
          }
        });

    // 监听消息变化，更新建议
    this.messageService.created$
        .pipe(takeUntilDestroyed())
        .subscribe((message) => {
          if (message && message.sessionId) {
            this.currentSessionId = message.sessionId;
            this.currentMessageId = message.id;
            // 只有当消息不在生成中时才加载建议
            if (!message.isGenerating) {
              this.loadSuggestions(message.sessionId, message.id);
            }
          }
        });

    // 添加对isGenerating状态的监听，只在生成状态变为false时加载建议
    let lastGeneratingState = this.messageService.isGenerating;
    effect(() => {
      const currentGeneratingState = this.messageService.isGenerating;

      // 只在生成状态从真变为假时加载建议（生成完成时）
      if (lastGeneratingState === true && currentGeneratingState === false && this.currentSessionId) {
        // 添加延时，确保所有状态都已更新
        setTimeout(() => {
          this.loadSuggestions(this.currentSessionId!, this.currentMessageId);
        }, 500);
      }

      lastGeneratingState = currentGeneratingState;
    });
  }

  /**
   * 加载建议问题
   * @param sessionId 会话ID
   * @param messageId 最后一条消息ID
   */
  private loadSuggestions(sessionId: string, messageId?: string): void {
    // 再次检查消息生成状态，确保在生成过程中不加载建议
    if (this.messageService.isGenerating) {
      this.templates = [];
      this.reportTemplates = [];
      this.isLoading = false;
      return;
    }

    // 检查是否有消息正在生成中
    const anyMessageGenerating = this.messageService.messages.some(msg => msg.isGenerating);
    if (anyMessageGenerating) {
      this.templates = [];
      this.reportTemplates = [];
      this.isLoading = false;
      return;
    }

    this.isLoading = true;
    // 更新当前消息ID
    if (messageId) {
      this.currentMessageId = messageId;
    }

    // 加载建议问题
    this.suggestionService.getSuggestions(sessionId, messageId).subscribe({
      next: (templates) => {
        this.templates = templates;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('加载建议问题失败:', error);
        this.isLoading = false;
        // 加载失败时使用默认模板
        this.templates = [
          {
            id: 'clarify',
            text: '能否详细解释一下这个观点？',
            description: '请求更多解释',
          },
          {
            id: 'example',
            text: '能给我一些具体的例子吗？',
            description: '请求实例说明',
          },
        ];
      },
    });

    // 加载报告模板
    if (sessionId) {
      this.suggestionService
          .getReportTemplates(sessionId, messageId)
          .subscribe({
            next: (templates) => {
              this.reportTemplates = templates;
            },
            error: (error) => {
              console.error('加载报告模板失败:', error);
              this.reportTemplates = [];
            },
          });
    }
  }

  // 默认使用第一个报告模板
  get reportTemplate(): SuggestionTemplate {
    return this.reportTemplates[0];
  }

  /**
   * 切换建议面板的折叠状态
   * @param event 可选的事件对象，用于阻止事件冒泡
   */
  toggleCollapse(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.isCollapsed = !this.isCollapsed;
  }

  /**
   * 获取文本的简短版本，用于缩略按钮显示
   * @param text 原始文本
   * @returns 原始文本，不再截断
   */
  getShortText(text: string): string {
    // 不再限制文本长度，由后端返回的内容自动确定
    return text;
  }

  /**
   * 获取报告标题
   * @param template 报告模板
   * @returns 报告标题
   */
  getReportTitle(template: SuggestionTemplate): string {
    if (template.id === 'comprehensive-report') {
      return '完整报告';
    } else if (template.id === 'executive-summary') {
      return '执行摘要';
    } else if (template.id === 'action-plan') {
      return '行动计划';
    } else {
      return template.id;
    }
  }

  /**
   * 处理模板点击事件
   * @param template 选中的模板
   * @param event 可选的点击事件
   */
  onTemplateClick(template: SuggestionTemplate, event?: MouseEvent): void {
    // 如果提供了事件对象，阻止事件冒泡
    if (event) {
      event.stopPropagation();
    }

    // 发送模板文本到父组件
    this.templateSelected.emit(template.text);

    // 记录用户选择的建议问题
    if (this.currentSessionId && this.currentMessageId) {
      this.suggestionService
          .recordSuggestionUsage(
              template,
              this.currentSessionId,
              this.currentMessageId,
          )
          .subscribe({
            next: (response) => {
              // 记录成功
            },
            error: (error) => {
              console.error('记录建议问题使用失败:', error);
            },
          });
    }
  }
}
