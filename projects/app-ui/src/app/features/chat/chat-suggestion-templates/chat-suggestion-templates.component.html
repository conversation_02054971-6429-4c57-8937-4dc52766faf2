<!-- 只有当没有消息正在生成时才显示建议和报告 -->
@if (!messageService.isGenerating) {
  <div class="suggestion-templates-container" [class.collapsed]="isCollapsed">
    <!-- 整个标题栏可点击折叠/展开 -->
    <div class="suggestion-header-row" (click)="toggleCollapse()" (keydown.enter)="toggleCollapse()" tabindex="0"
         role="button">
      <!-- 标题和按钮容器 -->
      <div class="suggestion-content-wrapper">
        <!-- 标题 -->
        <div class="templates-title">后续建议</div>

        <!-- 折叠状态下的缩略按钮区域 - 与标题在同一行 -->
        @if (isCollapsed && (templates.length > 0 || reportTemplates.length > 0)) {
          <div class="collapsed-buttons-inline" (click)="$event.stopPropagation()">
            <!-- 报告缩略按钮 - 放在前面 -->
            @if (reportTemplates.length > 0) {
              <div class="collapsed-reports">
                @for (template of reportTemplates; track template.id) {
                  <button
                      mat-button
                      class="collapsed-button report-button"
                      [matTooltip]="template.description"
                      (click)="onTemplateClick(template, $event)">
                    @if (template.icon === 'description') {
                      <mat-icon svgIcon="description"></mat-icon>
                    } @else if (template.icon === 'summarize') {
                      <mat-icon svgIcon="summarize"></mat-icon>
                    } @else if (template.icon === 'checklist') {
                      <mat-icon svgIcon="checklist"></mat-icon>
                    } @else {
                      <mat-icon svgIcon="document"></mat-icon>
                    }
                    <span>{{ getReportTitle(template) }}</span>
                  </button>
                }
              </div>
            }

            <!-- 后续消息缩略按钮 - 放在后面 -->
            @if (templates.length > 0) {
              <div class="collapsed-suggestions">
                <!-- 当有报告时只显示1项建议问题，否则显示3项 -->
                @for (template of templates.slice(0, reportTemplates.length > 0 ? 1 : 3); track template.id) {
                  <button
                      mat-button
                      class="collapsed-button suggestion-button"
                      [matTooltip]="template.text"
                      (click)="onTemplateClick(template, $event)">
                    {{ getShortText(template.text) }}
                  </button>
                }
                <!-- 当有报告时，如果建议问题超过1项则显示+n，否则如果超过3项显示+n -->
                @if ((reportTemplates.length > 0 && templates.length > 1) || (reportTemplates.length === 0 && templates.length > 3)) {
                  <button
                      mat-button
                      class="collapsed-button more-button"
                      [matTooltip]="'查看更多建议问题'"
                      (click)="toggleCollapse($event)">
                    +{{ reportTemplates.length > 0 ? templates.length - 1 : templates.length - 3 }}
                  </button>
                }
              </div>
            }
          </div>
        }
      </div>

      <!-- 折叠图标放在最右侧 -->
      <div class="toggle-icon">
        @if (isCollapsed) {
          <mat-icon svgIcon="chevron-down"></mat-icon>
        } @else {
          <mat-icon svgIcon="chevron-up"></mat-icon>
        }
      </div>
    </div>

    <!-- 报告生成选项，根据折叠状态显示或隐藏 - 放在前面 -->
    @if (!isCollapsed && reportTemplates.length > 0) {
      <div class="report-section">
        <div class="report-section-title">生成总结报告：</div>
        <div class="report-cards">
          @for (template of reportTemplates; track template.id) {
            <div class="report-card" (click)="onTemplateClick(template, $event)"
                 (keydown.enter)="onTemplateClick(template)"
                 tabindex="0" role="button">
              <div class="report-icon">
                <mat-icon [svgIcon]="template.icon ?? 'report'"></mat-icon>
              </div>
              <div class="report-content">
                <div class="report-title">{{ getReportTitle(template) }}</div>
                <div class="report-description">{{ template.description }}</div>
              </div>
            </div>
          }
        </div>
      </div>
    }

    <!-- 建议的后续问题部分，根据折叠状态显示或隐藏 - 放在后面 -->
    @if (!isCollapsed && (templates.length > 0 || isLoading)) {
      <div class="suggestion-content">
        <div class="templates-list">
          @if (isLoading) {
            <div class="loading-placeholder">正在生成建议问题...</div>
          } @else if (templates.length === 0) {
            <div class="empty-placeholder">暂无建议问题</div>
          } @else {
            @for (template of templates; track template.id) {
              <button
                  mat-button
                  class="template-button"
                  [matTooltip]="template.description"
                  (click)="onTemplateClick(template, $event)">
                {{ template.text }}
              </button>
            }
          }
        </div>
      </div>
    }
  </div>
}
