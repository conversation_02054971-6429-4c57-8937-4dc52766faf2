import { Component, effect, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { ChatMessage } from '../_services/chat-message';
import { ChatMessageGenerationStep } from '../_services/chat-message-generation-step';
import { ChatGenerationPhaseType } from '../_services/chat-generation-phase-type';

/**
 * 阶段分组接口
 */
interface PhaseGroup {
  phase: ChatGenerationPhaseType;
  phaseName: string;
  steps: ChatMessageGenerationStep[];
}

/**
 * 生成中状态组件
 * 显示消息生成过程中的不同阶段
 */
@Component({
  selector: 'app-chat-generation-status',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
  ],
  templateUrl: './chat-generation-status.component.html',
  styleUrl: './chat-generation-status.component.scss',
  // 移除动画效果
})
export class ChatGenerationStatusComponent {
  @Input()
  message!: ChatMessage;

  get steps(): ChatMessageGenerationStep[] {
    return this.message.steps;
  }

  // 展开/折叠状态
  protected expanded = false;

  // 阶段分组
  protected phaseGroups: PhaseGroup[] = [];

  constructor() {
    // 使用 effect 监听 steps 的变化并更新 phaseGroups
    effect(() => {
      this.updatePhaseGroups();
    });
  }

  protected get activeStepName() {
    // 如果消息不在生成中，直接显示“显示思路”
    if (!this.message.isGenerating) {
      return '显示思路';
    }

    // 如果没有步骤或所有步骤都已完成，显示“显示思路”
    const activeStep = this.steps[this.steps.length - 1];

    return activeStep?.name ?? '显示思路';
  }

  /**
   * 切换展开/折叠状态
   */
  toggleExpanded(): void {
    this.expanded = !this.expanded;

    // 如果展开，确保阶段分组是最新的
    if (this.expanded) {
      this.updatePhaseGroups();
    }
  }

  /**
   * 判断是否为当前活动阶段
   * @param step 阶段信息
   * @returns 是否为当前活动阶段
   */
  isActiveStep(step: ChatMessageGenerationStep): boolean {
    // 找到第一个未完成的阶段，即为当前活动阶段
    const activeStep = this.steps.find((p) => !p.completed);
    return activeStep?.id === step.id;
  }

  /**
   * 获取阶段名称
   * @param phase 阶段类型
   * @returns 阶段名称
   */
  getPhaseDisplayName(phase: ChatGenerationPhaseType): string {
    const phaseNames: Record<ChatGenerationPhaseType, string> = {
      waiting: '等待阶段',
      understanding: '理解阶段',
      planning: '规划阶段',
      searching: '检索阶段',
      analyzing: '分析阶段',
      generating: '生成阶段',
      completed: '完成阶段',
    };
    return phaseNames[phase] || phase;
  }

  /**
   * 更新阶段分组
   */
  protected updatePhaseGroups(): void {
    // 如果没有步骤，设置为空数组
    if (!this.steps.length) {
      this.phaseGroups = [];
      return;
    }

    // 按阶段分组
    const groupedByPhase = this.steps.reduce<
      Record<string, ChatMessageGenerationStep[]>
    >((groups, step) => {
      const phase = step.phase;
      if (!groups[phase]) {
        groups[phase] = [];
      }
      groups[phase].push(step);
      return groups;
    }, {});

    // 转换为数组格式
    this.phaseGroups = Object.entries(groupedByPhase).map(([phase, steps]) => ({
      phase: phase as ChatGenerationPhaseType,
      phaseName: this.getPhaseDisplayName(phase as ChatGenerationPhaseType),
      steps,
    }));
  }
}
