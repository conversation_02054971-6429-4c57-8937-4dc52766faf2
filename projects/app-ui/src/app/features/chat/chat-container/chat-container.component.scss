.chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: 0 var(--spacing-xs) var(--spacing-md) var(--shadow-light);

  @media (max-width: 768px) {
    border-radius: 0; /* 移动端去掉圆角，充分利用空间 */
    box-shadow: none; /* 移动端去掉阴影 */
  }

  app-chat-message-list {
    flex: auto;
  }
}
