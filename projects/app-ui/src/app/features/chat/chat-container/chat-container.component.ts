import {Component, OnInit, ViewChild} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatSnackBarModule} from '@angular/material/snack-bar';
import {ActivatedRoute, ParamMap} from '@angular/router';
import {ChatSessionListComponent} from '../chat-session-list/chat-session-list.component';
import {ChatMessageListComponent} from '../chat-message-list/chat-message-list.component';
import {ChatInputComponent} from '../chat-input/chat-input.component';
import {ChatSuggestionTemplatesComponent} from '../chat-suggestion-templates/chat-suggestion-templates.component';
import {ChatSessionService} from '../_services/chat-session.service';
import {LocalStateHolder} from '../_services/last-session-holder.service';

@Component({
  selector: 'app-chat-container',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    ChatSessionListComponent,
    ChatMessageListComponent,
    ChatInputComponent,
    ChatSuggestionTemplatesComponent,
  ],
  templateUrl: './chat-container.component.html',
  styleUrl: './chat-container.component.scss',
})
export class ChatContainerComponent implements OnInit {
  @ViewChild(ChatInputComponent) chatInput!: ChatInputComponent;

  constructor(
      private route: ActivatedRoute,
      private sessionService: ChatSessionService,
      private localStorageService: LocalStateHolder,
  ) {
  }

  ngOnInit(): void {
    // 从路由参数中获取会话ID
    this.route.paramMap.subscribe((params: ParamMap) => {
      const sessionId = params.get('sessionId');
      if (sessionId) {
        // 如果URL中有会话ID，选择该会话
        this.sessionService.selectById(sessionId);
        // 保存最后活跃的会话ID
        this.localStorageService.saveLastSessionId(sessionId);
      }
    });
  }

  /**
   * 处理模板选择事件
   * @param templateText 模板文本
   */
  onTemplateSelected(templateText: string) {
    if (this.chatInput) {
      this.chatInput.setMessageContent(templateText, null);
    }
  }
}
