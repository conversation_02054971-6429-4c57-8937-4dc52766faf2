import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

/**
 * 数据库 API 服务
 * 用于处理数据库相关的操作
 */
@Injectable({ providedIn: 'root' })
export class DatabaseApiService {
  private apiUrl = `${environment.apiBaseUrl}/database`;

  constructor(private http: HttpClient) {}

  /**
   * 重置数据库
   * 清空所有数据并重新初始化演示数据
   */
  resetDatabase(): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(`${this.apiUrl}/reset`, {});
  }
}
