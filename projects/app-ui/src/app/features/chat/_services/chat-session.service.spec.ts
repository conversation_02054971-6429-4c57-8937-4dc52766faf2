import { TestBed } from '@angular/core/testing';
import { BehaviorSubject, of } from 'rxjs';
import { ChatSessionService } from './chat-session.service';
import { ChatSessionApi } from './chat-session-api.service';
import { ChatSession } from './chat-session';
import { ChatSessionEvent } from './chat-session-event';
import { AuthService } from '../../auth/_services/auth.service';
import { LocalStateHolder } from './last-session-holder.service';

describe('ChatSessionService', () => {
  let service: ChatSessionService;
  let sessionApiMock: any;
  let authServiceMock: any;
  let localStorageServiceMock: any;
  let isAuthenticatedSubject: BehaviorSubject<boolean>;

  // 模拟数据
  const mockSessions: ChatSession[] = [
    {
      id: 'session-1',
      name: '测试会话1',
      userId: 'user-1',
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
    },
    {
      id: 'session-2',
      name: '测试会话2',
      userId: 'user-1',
      createdAt: new Date('2023-01-02T00:00:00Z'),
      updatedAt: new Date('2023-01-02T00:00:00Z'),
    },
  ];

  beforeEach(() => {
    // 创建模拟服务
    sessionApiMock = {
      query: jest.fn().mockReturnValue(of(mockSessions)),
      fetch: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      getEventStream: jest.fn().mockReturnValue(of({} as ChatSessionEvent)),
    };

    // 创建认证服务模拟
    isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
    authServiceMock = {
      isAuthenticated$: isAuthenticatedSubject.asObservable(),
      isAuthenticated: jest.fn().mockReturnValue(false),
    };

    // 创建本地存储服务模拟
    localStorageServiceMock = {
      saveLastSessionId: jest.fn(),
      getLastSessionId: jest.fn().mockReturnValue(undefined),
      clearLastSessionId: jest.fn(),
    };

    TestBed.configureTestingModule({
      providers: [
        ChatSessionService,
        { provide: ChatSessionApi, useValue: sessionApiMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: LocalStateHolder, useValue: localStorageServiceMock },
      ],
    });

    service = TestBed.inject(ChatSessionService);

    // 重置模拟函数的调用计数
    jest.clearAllMocks();
  });

  it('应该创建服务', () => {
    expect(service).toBeTruthy();
  });

  it('未登录时不应该加载会话列表或订阅事件', () => {
    expect(sessionApiMock.query).not.toHaveBeenCalled();
    expect(sessionApiMock.getEventStream).not.toHaveBeenCalled();
    expect(service.sessions.length).toBe(0);
  });

  it('登录后应该加载会话列表', async () => {
    // 模拟 API 返回会话列表
    sessionApiMock.query.mockReturnValue(of(mockSessions));

    // 模拟用户登录
    isAuthenticatedSubject.next(true);

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 0));

    // 验证是否加载会话列表
    expect(sessionApiMock.query).toHaveBeenCalled();
    expect(service.sessions.length).toBe(2);
  });

  it('注销后应该清空会话列表', async () => {
    // 模拟 API 返回会话列表
    sessionApiMock.query.mockReturnValue(of(mockSessions));

    // 先模拟用户登录
    isAuthenticatedSubject.next(true);

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 0));

    expect(service.sessions.length).toBe(2);

    // 再模拟用户注销
    isAuthenticatedSubject.next(false);
    expect(service.sessions.length).toBe(0);
  });

  describe('会话选择', () => {
    it('应该能够选择会话', () => {
      service.selection = mockSessions[0];
      expect(service.selection).toBe(mockSessions[0]);
    });

    it('应该能够通过ID选择会话', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      // 现在选择会话
      service.selectById('session-2');
      expect(service.selection?.id).toBe('session-2');
      expect(localStorageServiceMock.saveLastSessionId).toHaveBeenCalledWith(
        'session-2'
      );
    });

    it('当传入undefined时应该清除选择', () => {
      service.selection = mockSessions[0];
      service.selectById(undefined);
      expect(service.selection).toBeUndefined();
    });

    it('当传入不存在的ID时不应该改变选择', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      // 设置初始选择
      service.selection = mockSessions[0];

      // 选择不存在的会话
      service.selectById('non-existent');
      expect(service.selection).toBeUndefined();
    });

    it('登录后如果没有上次活跃的会话，应该不选择任何会话', () => {
      // 确保没有上次活跃的会话ID
      localStorageServiceMock.getLastSessionId.mockReturnValue(undefined);

      // 模拟用户登录
      isAuthenticatedSubject.next(true);

      // 验证是否没有选择任何会话
      expect(service.selection).toBeUndefined();
    });

    it('登录后应该自动选择上次活跃的会话（如果存在）', async () => {
      // 设置上次活跃的会话ID
      localStorageServiceMock.getLastSessionId.mockReturnValue('session-1');

      // 模拟 API 返回会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));

      // 模拟用户登录
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      // 验证是否自动选择了上次活跃的会话
      expect(service.selection?.id).toBe('session-1');
    });

    it('如果上次活跃的会话不存在，应该不选择任何会话', () => {
      // 设置一个不存在的会话ID
      localStorageServiceMock.getLastSessionId.mockReturnValue('non-existent');

      // 模拟用户登录
      isAuthenticatedSubject.next(true);

      // 验证是否没有选择任何会话
      expect(service.selection).toBeUndefined();
    });
  });

  describe('会话操作', () => {
    it('应该根据ID获取会话', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      const session = service.getById('session-1');
      expect(session).toBe(mockSessions[0]);
    });

    it('应该创建新会话', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      const newSession: ChatSession = {
        id: 'session-3',
        name: '新会话',
        userId: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      sessionApiMock.create.mockReturnValue(of(newSession));

      const session = await service.create('新会话');
      expect(session).toBe(newSession);
      expect(sessionApiMock.create).toHaveBeenCalledWith({
        name: '新会话',
      });

      // 验证内存中的会话是否被添加
      expect(service.sessions.length).toBe(3);
      expect(service.getById('session-3')).toBe(newSession);
    });

    it('应该更新会话', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      const updatedSession: ChatSession = {
        ...mockSessions[0],
        name: '更新后的会话名称',
      };

      sessionApiMock.update.mockReturnValue(of(updatedSession));

      const session = await service.update('session-1', {
        name: '更新后的会话名称',
      });
      expect(session).toBe(updatedSession);
      expect(sessionApiMock.update).toHaveBeenCalledWith('session-1', {
        name: '更新后的会话名称',
      });

      // 验证内存中的会话是否被更新
      const updatedInMemory = service.getById('session-1');
      expect(updatedInMemory?.name).toBe('更新后的会话名称');
    });

    it('在更新不存在的会话时应该抛出错误', async () => {
      await expect(
        service.update('non-existent', { name: '新名称' })
      ).rejects.toThrow(/non-existent/);
    });

    it('应该删除会话', async () => {
      // 首先需要让服务加载会话列表
      sessionApiMock.query.mockReturnValue(of(mockSessions));
      isAuthenticatedSubject.next(true);

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 0));

      sessionApiMock.delete.mockReturnValue(of(void 0));

      await service.delete('session-1');
      expect(sessionApiMock.delete).toHaveBeenCalledWith('session-1');

      // 验证内存中的会话是否被删除
      expect(service.getById('session-1')).toBeUndefined();
      expect(service.sessions.length).toBe(1);
    });

    it('删除不存在的会话时应该正常返回', async () => {
      await service.delete('non-existent');
      expect(sessionApiMock.delete).not.toHaveBeenCalled();
    });
  });
});
