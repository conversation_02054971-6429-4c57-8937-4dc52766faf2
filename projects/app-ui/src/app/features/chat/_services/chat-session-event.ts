import { ChatSession } from './chat-session';

/**
 * 会话事件基础接口
 */
export interface BaseSessionEvent {
  type: string;
}

/**
 * 会话创建事件
 */
export interface SessionCreatedEvent extends BaseSessionEvent {
  type: 'created';
  sessionId: string;
  data: ChatSession;
}

/**
 * 会话更新事件
 */
export interface SessionUpdatedEvent extends BaseSessionEvent {
  type: 'updated';
  sessionId: string;
  data: ChatSession;
}

/**
 * 会话删除事件
 */
export interface SessionDeletedEvent extends BaseSessionEvent {
  type: 'deleted';
  sessionId: string;
}

/**
 * 会话选中事件
 */
export interface SessionSelectedEvent extends BaseSessionEvent {
  type: 'selected';
  sessionId: string;
  data: ChatSession;
}

/**
 * 会话事件类型
 */
export type ChatSessionEvent =
  | SessionCreatedEvent
  | SessionUpdatedEvent
  | SessionDeletedEvent
  | SessionSelectedEvent;
