import { ChatMessage } from './chat-message';
import { ChatMessageGenerationStep } from './chat-message-generation-step';

/**
 * 消息事件基础接口
 */
export interface BaseChatMessageEvent {
  type: string;
  messageId: string;
}

/**
 * 消息生成内容事件
 */
export interface MessageStreamingContentEvent extends BaseChatMessageEvent {
  type: 'streaming-content';
  text: string;
  index: number;
}

/**
 * 消息生成阶段事件
 */
export interface MessageGenerationStepEvent extends BaseChatMessageEvent {
  type: 'generation-steps';
  steps: ChatMessageGenerationStep[];
}

/**
 * 消息生成完成事件
 */
export interface MessageGenerationCompleteEvent extends BaseChatMessageEvent {
  type: 'generation-complete';
  message: ChatMessage;
}

/**
 * 消息事件类型
 */
export type ChatMessageEvent =
  | MessageStreamingContentEvent
  | MessageGenerationStepEvent
  | MessageGenerationCompleteEvent;
