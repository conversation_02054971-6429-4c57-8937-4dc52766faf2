import { TestBed } from '@angular/core/testing';
import { BehaviorSubject, of } from 'rxjs';
import { ChatMessageService } from './chat-message.service';
import { ChatMessageApi } from './chat-message-api.service';
import { ChatSessionService } from './chat-session.service';
import { ChatMessage } from './chat-message';
import { ChatSession } from './chat-session';
import {
  MessageGenerationCompleteEvent,
  MessageGenerationInitEvent,
  MessageGenerationStepsEvent,
  MessageStreamingContentEvent,
} from './chat-message-event';
import { ChatMessageFeedback } from './chat-message-feedback';
import { AuthService } from '../../auth/_services/auth.service';

describe('ChatMessageService', () => {
  let service: ChatMessageService;
  let messageApiMock: any;
  let sessionServiceMock: any;
  let authServiceMock: any;
  let isAuthenticatedSubject: BehaviorSubject<boolean>;
  let sessionSelectionSubject: BehaviorSubject<ChatSession | undefined>;

  // 模拟数据
  const mockSession: ChatSession = {
    id: 'session-1',
    name: '测试会话',
    userId: 'user-1',
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  };

  const mockMessages: ChatMessage[] = [
    {
      id: 'message-1',
      sessionId: 'session-1',
      role: 'user',
      content: '你好',
      createdAt: new Date('2023-01-01T01:00:00Z'),
    },
    {
      id: 'message-2',
      sessionId: 'session-1',
      role: 'assistant',
      content: '你好！有什么我可以帮助你的吗？',
      createdAt: new Date('2023-01-01T01:01:00Z'),
      assistantName: '智能助手',
    },
  ];

  // 模拟事件流
  const mockEventStream = {
    subscribe: jest.fn().mockReturnValue({
      unsubscribe: jest.fn(),
    }),
  };

  beforeEach(() => {
    // 创建模拟服务
    messageApiMock = {
      queryBySessionId: jest.fn().mockReturnValue(of(mockMessages)),
      fetch: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      like: jest.fn(),
      dislike: jest.fn(),
      addFeedback: jest.fn(),
      regenerate: jest.fn(),
      editAndResend: jest.fn(),
      getEventStream: jest.fn().mockReturnValue(mockEventStream),
    };

    // 创建会话服务模拟
    sessionSelectionSubject = new BehaviorSubject<ChatSession | undefined>(
      undefined
    );
    sessionServiceMock = {
      getById: jest.fn().mockReturnValue(mockSession),
      selection$: sessionSelectionSubject.asObservable(),
    };

    // 创建认证服务模拟
    isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
    authServiceMock = {
      isAuthenticated$: isAuthenticatedSubject.asObservable(),
      isAuthenticated: jest.fn().mockReturnValue(false),
    };

    // 模拟返回值已在初始化时设置

    TestBed.configureTestingModule({
      providers: [
        ChatMessageService,
        { provide: ChatMessageApi, useValue: messageApiMock },
        { provide: ChatSessionService, useValue: sessionServiceMock },
        { provide: AuthService, useValue: authServiceMock },
      ],
    });

    service = TestBed.inject(ChatMessageService);
  });

  it('应该创建服务', () => {
    expect(service).toBeTruthy();
  });

  it('未登录时不应该加载消息或订阅事件', () => {
    expect(messageApiMock.queryBySessionId).not.toHaveBeenCalled();
    expect(messageApiMock.getEventStream).not.toHaveBeenCalled();
    expect(service.messages.length).toBe(0);
  });

  it('登录后不应该自动订阅事件，但在选择会话前不应该加载消息', () => {
    // 模拟用户登录
    isAuthenticatedSubject.next(true);

    // 验证是否订阅事件（现在不应该自动订阅）
    expect(messageApiMock.getEventStream).not.toHaveBeenCalled();

    // 验证在选择会话前不应该加载消息
    expect(messageApiMock.queryBySessionId).not.toHaveBeenCalled();
    expect(service.messages.length).toBe(0);
  });

  it('登录并选择会话后应该加载消息', async () => {
    // 模拟用户登录
    isAuthenticatedSubject.next(true);

    // 模拟选择会话
    sessionSelectionSubject.next(mockSession);

    // 等待加载完成
    await Promise.resolve(); // 等待异步操作完成

    // 验证是否加载消息
    expect(messageApiMock.queryBySessionId).toHaveBeenCalledWith('session-1');
    expect(service.messages.length).toBe(2);
  });

  it('注销后应该清空消息列表并取消订阅事件', async () => {
    // 先模拟用户登录并选择会话
    isAuthenticatedSubject.next(true);
    sessionSelectionSubject.next(mockSession);

    // 等待加载完成
    await Promise.resolve(); // 等待异步操作完成
    expect(service.messages.length).toBe(2);

    // 模拟选择会话变为空，这将清空消息列表
    sessionSelectionSubject.next(undefined);
    await Promise.resolve(); // 等待异步操作完成
    expect(service.messages.length).toBe(0);
  });

  describe('消息事件处理', () => {
    it('应该处理消息流式内容事件', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');
      expect(service.messages.length).toBe(2);

      // 模拟流式内容事件
      const streamingEvent: MessageStreamingContentEvent = {
        type: 'streaming-content',
        messageId: mockMessages[1].id,
        text: '新的内容',
        isFirst: false,
      };

      // 手动触发事件处理
      (service as any).handleStreamingContent(streamingEvent);

      // 验证消息内容是否被更新
      const message = service.getById(mockMessages[1].id);
      expect(message?.content).toContain('新的内容');
    });

    it('应该处理消息生成阶段事件', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');
      expect(service.messages.length).toBe(2);

      // 模拟生成阶段事件
      const phasesEvent: MessageGenerationStepsEvent = {
        type: 'generation-steps',
        messageId: mockMessages[1].id,
        steps: [
          {
            type: 'understanding',
            name: '正在理解问题',
            output: '',
            completed: true,
          },
          {
            type: 'planning',
            name: '正在拟定计划',
            output: '',
            completed: false,
          },
        ],
      };

      // 手动触发事件处理
      (service as any).handleGenerationPhases(phasesEvent);

      // 验证消息阶段是否被更新
      const message = service.getById(mockMessages[1].id);
      expect(message?.steps).toEqual(phasesEvent.steps);
    });

    it('应该处理消息生成初始化事件', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');
      expect(service.messages.length).toBe(2);

      // 准备助理消息
      const assistantMessage: ChatMessage = {
        id: 'message-3',
        sessionId: 'session-1',
        role: 'assistant',
        content: '',
        createdAt: new Date(),
        assistantName: '智能助理',
        isGenerating: true,
        steps: [
          {
            type: 'understanding',
            name: '正在理解问题',
            output: '',
            completed: false,
          },
        ],
      };

      // 模拟生成初始化事件
      const initEvent: MessageGenerationInitEvent = {
        type: 'generation-init',
        messageId: assistantMessage.id,
        message: assistantMessage,
      };

      // 手动触发事件处理
      (service as any).handleGenerationInit(initEvent);

      // 验证消息是否被添加到缓存
      expect(service.messages.length).toBe(3);
      const cachedMessage = service.getById(assistantMessage.id);
      expect(cachedMessage).toBeTruthy();
      expect(cachedMessage.id).toBe(assistantMessage.id);
      expect(cachedMessage.role).toBe('assistant');
      expect(cachedMessage.isGenerating).toBe(true);
    });

    it('应该处理消息生成完成事件', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');
      expect(service.messages.length).toBe(2);

      // 准备完成的消息
      const completedMessage: ChatMessage = {
        ...mockMessages[1],
        content: '生成完成的内容',
        isGenerating: false,
      };

      // 模拟生成完成事件
      const completeEvent: MessageGenerationCompleteEvent = {
        type: 'generation-complete',
        messageId: completedMessage.id,
        message: completedMessage,
      };

      // 模拟订阅对象
      const mockSubscription = { unsubscribe: jest.fn() };
      messageApiMock.getEventStream.mockReturnValue(mockEventStream);
      mockEventStream.subscribe.mockReturnValue(mockSubscription);
      (service as any).eventSubscription = mockSubscription;

      // 手动触发事件处理
      (service as any).handleGenerationComplete(completeEvent);

      // 验证消息是否被更新
      const updatedMessage = service.getById(completedMessage.id);
      expect(updatedMessage?.content).toBe('生成完成的内容');
      expect(updatedMessage?.isGenerating).toBe(false);

      // 验证是否取消订阅
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });
  });

  describe('加载和查询消息', () => {
    it('应该加载会话的所有消息', async () => {
      const messages = await service.loadForSession('session-1');
      expect(messages.length).toBe(2);
      expect(messageApiMock.queryBySessionId).toHaveBeenCalledWith('session-1');
    });

    it('应该根据会话ID查询消息', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      // 直接使用 messages 属性获取消息
      const messages = service.messages;
      expect(messages.length).toBe(2);
    });

    it('应该根据消息ID获取消息', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      const message = service.getById('message-1');
      expect(message).toBeTruthy();
      expect(message?.id).toBe('message-1');
    });

    it('应该从服务器获取消息', async () => {
      const fetchedMessage: ChatMessage = {
        id: 'message-1',
        sessionId: 'session-1',
        role: 'user',
        content: '更新后的内容',
        createdAt: new Date(),
      };

      messageApiMock.fetch.mockReturnValue(of(fetchedMessage));

      const message = await service.fetchById('message-1');
      expect(message).toBe(fetchedMessage);
      expect(messageApiMock.fetch).toHaveBeenCalledWith('message-1');

      // 验证内存中的消息是否被更新
      const updatedMessage = service.getById('message-1');
      expect(updatedMessage?.content).toBe('更新后的内容');
    });
  });

  describe('发送消息', () => {
    it('应该发送用户消息并订阅事件流', async () => {
      const newMessage: ChatMessage = {
        id: 'message-3',
        sessionId: 'session-1',
        role: 'user',
        content: '新消息',
        createdAt: new Date(),
      };

      messageApiMock.create.mockReturnValue(of(newMessage));

      const message = await service.send('session-1', '新消息');
      expect(message).toEqual(newMessage); // 使用 toEqual 而不是 toBe
      expect(messageApiMock.create).toHaveBeenCalledWith({
        sessionId: 'session-1',
        content: '新消息',
        attachments: undefined,
      });

      // 验证是否订阅事件流
      expect(messageApiMock.getEventStream).toHaveBeenCalled();

      // 验证内存中的消息是否被添加
      expect(service.messages.length).toBe(1);
      expect(service.messages[0]).toEqual(newMessage); // 使用 toEqual 而不是 toBe
    });

    it('应该在会话不存在时抛出错误', async () => {
      sessionServiceMock.getById.mockReturnValue(undefined);

      await expect(service.send('non-existent', '新消息')).rejects.toThrow(
        /non-existent/
      );
    });
  });

  describe('消息操作', () => {
    it('应该删除消息并更新本地缓存', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');
      expect(service.messages.length).toBe(2);

      messageApiMock.delete.mockReturnValue(of(void 0));

      await service.delete('message-1');
      expect(messageApiMock.delete).toHaveBeenCalledWith('message-1');

      // 验证消息是否从本地缓存中删除
      expect(service.messages.length).toBe(1);
      expect(service.getById('message-1')).toBeUndefined();
    });

    it('应该点赞消息并更新本地缓存', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      const likedMessage: ChatMessage = {
        ...mockMessages[0],
        liked: true,
      };

      messageApiMock.like.mockReturnValue(of(likedMessage));

      const message = await service.like('message-1');
      expect(message).toEqual(likedMessage); // 使用 toEqual 而不是 toBe
      expect(messageApiMock.like).toHaveBeenCalledWith('message-1');

      // 验证消息是否在本地缓存中更新
      const cachedMessage = service.getById('message-1');
      expect(cachedMessage).toEqual(likedMessage); // 使用 toEqual 而不是 toBe
      expect(cachedMessage?.liked).toBe(true);
    });

    it('应该点踩消息并更新本地缓存', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      const dislikedMessage: ChatMessage = {
        ...mockMessages[0],
        disliked: true,
      };

      messageApiMock.dislike.mockReturnValue(of(dislikedMessage));

      const message = await service.dislike('message-1');
      expect(message).toEqual(dislikedMessage); // 使用 toEqual 而不是 toBe
      expect(messageApiMock.dislike).toHaveBeenCalledWith('message-1');

      // 验证消息是否在本地缓存中更新
      const cachedMessage = service.getById('message-1');
      expect(cachedMessage).toEqual(dislikedMessage); // 使用 toEqual 而不是 toBe
      expect(cachedMessage?.disliked).toBe(true);
    });

    it('应该添加消息反馈并更新本地缓存', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      const feedback: ChatMessageFeedback = {
        rate: 1,
        content: '信息不准确',
      };

      const messageWithFeedback: ChatMessage = {
        ...mockMessages[0],
        disliked: true,
        feedback,
      };

      messageApiMock.addFeedback.mockReturnValue(of(messageWithFeedback));

      const message = await service.addFeedback('message-1', feedback);
      expect(message).toEqual(messageWithFeedback); // 使用 toEqual 而不是 toBe
      expect(messageApiMock.addFeedback).toHaveBeenCalledWith(
        'message-1',
        feedback
      );

      // 验证消息是否在本地缓存中更新
      const cachedMessage = service.getById('message-1');
      expect(cachedMessage).toEqual(messageWithFeedback); // 使用 toEqual 而不是 toBe
      expect(cachedMessage?.feedback).toEqual(feedback); // 使用 toEqual 而不是 toBe
    });
  });

  describe('事件订阅管理', () => {
    it('应该在收到生成完成事件后取消订阅', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      // 模拟订阅对象
      const mockSubscription = { unsubscribe: jest.fn() };

      // 设置模拟
      messageApiMock.getEventStream.mockReturnValue(mockEventStream);
      mockEventStream.subscribe.mockReturnValue(mockSubscription);

      // 手动调用订阅方法
      (service as any).subscribeToEvents();

      // 获取订阅回调
      const subscriber = mockEventStream.subscribe.mock.calls[0][0];

      // 模拟生成初始化事件
      const initEvent: MessageGenerationInitEvent = {
        type: 'generation-init',
        messageId: 'message-3',
        message: {
          id: 'message-3',
          sessionId: 'session-1',
          role: 'assistant',
          content: '',
          createdAt: new Date(),
          assistantName: '智能助理',
          isGenerating: true,
        },
      };

      // 触发初始化事件
      subscriber.next(initEvent);

      // 模拟生成完成事件
      const completeEvent: MessageGenerationCompleteEvent = {
        type: 'generation-complete',
        messageId: mockMessages[1].id,
        message: mockMessages[1],
      };

      // 触发完成事件
      subscriber.next(completeEvent);

      // 验证是否调用了取消订阅方法
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });
  });

  describe('高级操作', () => {
    it('应该重新生成消息并订阅事件流', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      // 确保消息类型是用户消息
      mockMessages[0].role = 'user';

      messageApiMock.regenerate.mockReturnValue(of(void 0));

      await service.regenerate('session-1', 'message-1');
      expect(messageApiMock.regenerate).toHaveBeenCalledWith(
        'session-1',
        'message-1'
      );

      // 验证是否订阅事件流
      expect(messageApiMock.getEventStream).toHaveBeenCalled();
    });

    it('应该在重新生成非用户消息时抛出错误', async () => {
      // 确保消息类型是助理消息
      mockMessages[0].role = 'assistant';

      await expect(() => {
        return service.regenerate('session-1', 'message-1');
      }).rejects.toThrow('只能重新生成用户消息的响应');
    });

    it('应该编辑并重新发送消息并订阅事件流', async () => {
      // 首先加载消息到缓存
      await service.loadForSession('session-1');

      messageApiMock.editAndResend.mockReturnValue(of(void 0));

      await service.editAndResend('message-1', '编辑后的内容');
      expect(messageApiMock.editAndResend).toHaveBeenCalledWith(
        'message-1',
        '编辑后的内容'
      );

      // 验证是否订阅事件流
      expect(messageApiMock.getEventStream).toHaveBeenCalled();
    });
  });
});
