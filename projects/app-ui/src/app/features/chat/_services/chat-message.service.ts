import { EventEmitter, Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { ChatMessageApi } from './chat-message-api.service';
import { ChatSessionService } from './chat-session.service';
import { ChatMessage } from './chat-message';
import {
  MessageGenerationCompleteEvent,
  MessageGenerationStepEvent,
  MessageStreamingContentEvent,
} from './chat-message-event';
import { Attachment } from './attachment';
import { ChatMessageFeedback } from './chat-message-feedback';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

/**
 * 消息服务
 * 负责管理聊天消息
 */
@Injectable({
  providedIn: 'root',
})
export class ChatMessageService {
  // 消息内容更新事件，用于通知UI组件消息内容已更新
  readonly contentUpdated$ = new EventEmitter<string>();
  readonly created$ = new EventEmitter<ChatMessage>();
  readonly loaded$ = new EventEmitter<readonly ChatMessage[]>();
  readonly editRequested$ = new EventEmitter<ChatMessage>();
  // 消息高亮事件，用于通知UI组件滚动到指定消息
  readonly highlightMessage$ = new EventEmitter<string>();

  // 使用常规属性存储消息，确保数据不可变性
  private _messages: ReadonlyArray<ChatMessage> = [];

  // 线性消息列表，根据 activeReplyId 构建
  private _linearMessages: ReadonlyArray<ChatMessage> = [];

  // 消息生成状态
  isGenerating = false;

  constructor(
    private messageApi: ChatMessageApi,
    private sessionService: ChatSessionService
  ) {
    // 监听当前会话的变化，并自动加载相应的消息
    this.sessionService.selection$
      .pipe(takeUntilDestroyed())
      .subscribe(async (session) => {
        if (session) {
          await this.loadForSession(session.id);
        } else {
          this.messages = [];
        }
      });
  }

  /**
   * 获取所有消息
   */
  get messages(): ReadonlyArray<ChatMessage> {
    return this._messages;
  }

  set messages(value: ReadonlyArray<ChatMessage>) {
    this._messages = value;
    this.buildLinearMessages();
    this.loaded$.next(value);
  }

  /**
   * 更新线性消息列表
   * 只在需要更新线性列表的地方调用此方法
   */
  private updateLinearMessages(): void {
    this.buildLinearMessages();
  }

  /**
   * 获取线性消息列表
   * 根据 activeReplyId 构建的线性消息列表
   */
  get linearMessages(): ReadonlyArray<ChatMessage> {
    return this._linearMessages;
  }

  /**
   * 根据 activeReplyId 构建线性消息列表
   * 从第一条消息开始，沿着 activeReplyId 链构建线性列表
   */
  private buildLinearMessages(): void {
    if (this._messages.length === 0) {
      this._linearMessages = [];
      return;
    }

    // 按创建时间排序，找到第一条消息
    const sortedMessages = [...this._messages].sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    const result: ChatMessage[] = [];
    let currentMessage: ChatMessage | null = sortedMessages[0];

    // 从第一条消息开始，沿着 activeReplyId 链构建线性列表
    while (currentMessage) {
      result.push(currentMessage);

      const activeReplyId: string | undefined = currentMessage.activeReplyId;
      // 如果当前消息有 activeReplyId，则找到下一条消息
      if (activeReplyId) {
        currentMessage = this._messages.find(m => m.id === activeReplyId) || null;
      } else {
        // 如果没有 activeReplyId，则结束
        currentMessage = null;
      }
    }

    this._linearMessages = result;
  }

  /**
   * 加载会话的所有消息
   * @param sessionId 会话ID
   * @returns 消息数组的Promise
   */
  async loadForSession(sessionId: string): Promise<ReadonlyArray<ChatMessage>> {
    this.messages = await firstValueFrom(
      this.messageApi.queryBySessionId(sessionId)
    );

    return this.messages;
  }

  /**
   * 根据ID获取消息
   * @param messageId 消息ID
   * @returns 消息对象或undefined
   */
  getById(messageId: string): ChatMessage {
    const result = this._messages.find((it) => it.id === messageId);
    if (!result) {
      throw `指定的消息不存在：${messageId}`;
    }
    return result;
  }

  /**
   * 从服务器获取消息
   * @param messageId 消息ID
   * @returns 消息对象的Promise
   */
  async fetchById(messageId: string): Promise<ChatMessage> {
    const message = await firstValueFrom(this.messageApi.fetch(messageId));

    // 更新内存中的消息缓存
    const existingMessage = this._messages.find((m) => m.id === messageId);

    if (existingMessage) {
      // 如果消息已存在，则替换它
      Object.assign(existingMessage, message);
    } else {
      // 如果消息不存在，则添加它
      this.messages = [...this.messages, message];
    }

    return message;
  }

  private checkForSession(sessionId: string) {
    const session = this.sessionService.getById(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }
  }

  /**
   * 发送用户消息
   * @param sessionId 会话ID
   * @param content 消息内容
   * @param attachments 可选的附件数组
   * @param replyToId 可选的回复消息ID
   * @returns 创建的消息对象的Promise
   */
  async send(
    sessionId: string,
    content: string,
    attachments?: Attachment[],
    replyToId?: string
  ): Promise<ChatMessage> {
    this.checkForSession(sessionId);

    // 调用API创建用户消息
    const lastAssistantMessage = this.linearMessages[this.linearMessages.length - 1];
    const response = await firstValueFrom(
      this.messageApi.create({
        sessionId,
        content,
        attachments,
        replyToId: replyToId ?? lastAssistantMessage?.id,
      })
    );

    // 从响应中提取用户消息和助理消息
    const userMessage = response.userMessage;
    const assistantMessage = response.assistantMessage;

    if (lastAssistantMessage) {
      lastAssistantMessage.activeReplyId = userMessage.id;
    }

    this.created$.next(userMessage);

    // 更新内存中的消息缓存
    this.messages = [...this.messages, userMessage, assistantMessage];

    // 订阅助理消息的事件流
    this.subscribeToEvents(assistantMessage.id);

    return userMessage;
  }

  /**
   * 删除消息
   * @param messageId 要删除的消息ID
   * @returns 删除操作的Promise
   */
  async delete(messageId: string): Promise<void> {
    // 获取消息所属的会话ID
    const message = this.getById(messageId);
    const sessionId = message.sessionId;

    // 调用API删除消息
    await firstValueFrom(this.messageApi.delete(messageId));

    // 重新加载会话的所有消息，确保前端显示与后端一致
    await this.loadForSession(sessionId);
  }

  /**
   * 点赞消息
   * @param messageId 消息ID
   * @returns 更新后的消息对象的Promise
   */
  async like(messageId: string): Promise<ChatMessage> {
    const updatedMessage = await firstValueFrom(
      this.messageApi.like(messageId)
    );

    // 更新本地缓存
    const message = this.getById(messageId);
    Object.assign(message, updatedMessage);

    return message;
  }

  /**
   * 点踩消息
   * @param messageId 消息ID
   * @returns 更新后的消息对象的Promise
   */
  async dislike(messageId: string): Promise<ChatMessage> {
    const updatedMessage = await firstValueFrom(
      this.messageApi.dislike(messageId)
    );

    // 更新本地缓存
    const message = this.getById(messageId);
    Object.assign(message, updatedMessage);

    return message;
  }

  /**
   * 添加消息反馈
   * @param messageId 消息ID
   * @param feedback 反馈内容
   * @returns 更新后的消息对象的Promise
   */
  async addFeedback(
    messageId: string,
    feedback: ChatMessageFeedback
  ): Promise<ChatMessage> {
    const updatedMessage = await firstValueFrom(
      this.messageApi.addFeedback(messageId, feedback)
    );

    // 更新本地缓存
    const message = this.getById(messageId);
    Object.assign(message, updatedMessage);

    return message;
  }

  /**
   * 发送重新生成消息的命令
   * @param message 当前助理消息
   * @returns 操作的Promise
   */
  async regenerate(message: ChatMessage): Promise<ChatMessage> {
    if (message.role !== 'assistant') {
      throw new Error('只能重新生成助理消息');
    }

    // 调用API重新生成消息
    // API会返回新的助理消息对象
    const response = await firstValueFrom(
      this.messageApi.regenerate(message.id, message.sessionId)
    );

    // 获取新的助理消息
    const newAssistantMessage = response.assistantMessage;

    Object.assign(message, newAssistantMessage);

    // 确保消息状态正确
    message.isGenerating = true;

    // 如果有用户消息，更新其activeReplyId
    if (message.replyToId) {
      // 找到对应的用户消息
      const userMessage = this.getById(message.replyToId);
      if (userMessage) {
        // 加入回复列表
        userMessage.replyIds = (userMessage.replyIds ?? []).concat(message.id);
        // 更新用户消息的activeReplyId
        userMessage.activeReplyId = message.id;
      }
    }

    // 直接订阅新助理消息的事件流
    this.subscribeToEvents(message.id);

    // 发出高亮消息事件，通知UI组件滚动到该消息
    this.highlightMessage$.emit(message.id);

    // 重新生成消息会改变 activeReplyId，需要更新线性列表
    this.updateLinearMessages();

    // 返回新的助理消息
    return message;
  }

  /**
   * 编辑并重新发送消息
   * 此方法不会直接发送消息，而是发出一个事件，通知输入框组件将消息内容填充到输入框中
   * @param messageId 要编辑的消息ID
   * @returns 操作的Promise
   */
  async editAndResend(messageId: string): Promise<void> {
    // 获取要编辑的消息
    const message = this.getById(messageId);

    // 发出编辑请求事件，通知输入框组件
    this.editRequested$.emit(message);
  }

  /**
   * 切换用户消息的活跃回复
   * @param userMessageId 用户消息ID
   * @param assistantMessageId 要激活的助理消息ID
   * @returns 操作的Promise
   */
  async switchActiveReply(
    userMessageId: string,
    assistantMessageId: string
  ): Promise<void> {
    // 获取用户消息
    const userMessage = this.getById(userMessageId);

    // 检查是否为用户消息
    if (userMessage.role !== 'user') {
      throw new Error('只能切换用户消息的活跃回复');
    }

    // 检查助理消息ID是否在用户消息的回复列表中
    if (
      !userMessage.replyIds ||
      !userMessage.replyIds.includes(assistantMessageId)
    ) {
      throw new Error('指定的助理消息不是该用户消息的回复');
    }

    // 更新用户消息的activeReplyId
    userMessage.activeReplyId = assistantMessageId;

    // 调用API更新用户消息
    await firstValueFrom(
      this.messageApi.update(userMessageId, {
        activeReplyId: assistantMessageId,
      })
    );

    // 切换活跃回复会改变 activeReplyId，需要更新线性列表
    this.updateLinearMessages();

    // 发出高亮消息事件，通知UI组件滚动到该消息
    this.highlightMessage$.emit(assistantMessageId);
  }

  /**
   * 订阅消息的更新事件
   * 监听后端推送的消息更新事件，并更新内存中的消息缓存
   * 在收到完成事件后自动取消订阅
   * @param messageId 消息ID
   */
  private subscribeToEvents(messageId: string): void {
    this.isGenerating = true;
    // 创建新的订阅
    this.messageApi.getEventStream(messageId).subscribe({
      next: (event) => {
        // 根据事件类型处理内存缓存
        switch (event.type) {
          case 'streaming-content':
            this.handleStreamingContent(event);
            break;
          case 'generation-steps':
            this.handleGenerationSteps(event);
            break;
          case 'generation-complete':
            // 收到生成完成事件后，订阅会自动关闭
            this.handleGenerationComplete(event);
            break;
        }
      },
      error: (error) => {
        console.error(`消息 ${messageId} 的事件流错误`, error);
      },
      complete: () => {
        // 订阅完成
      },
    });
  }

  /**
   * 处理消息生成阶段事件
   */
  private handleGenerationSteps(event: MessageGenerationStepEvent): void {
    const message = this.getById(event.messageId);
    message.steps = event.steps;
    this.contentUpdated$.next(event.messageId);
  }

  /**
   * 处理消息流式内容事件
   */
  private handleStreamingContent(event: MessageStreamingContentEvent): void {
    const message = this.getById(event.messageId);
    if (event.index === 0) {
      message.content = event.text;
    } else {
      message.content += event.text;
    }

    // 发出消息内容更新事件，通知UI组件消息内容已更新
    this.contentUpdated$.next(event.messageId);
  }

  /**
   * 处理消息生成完成事件
   */
  private handleGenerationComplete(
    event: MessageGenerationCompleteEvent
  ): void {
    this.isGenerating = false;
    const message = this.getById(event.messageId);
    Object.assign(message, event.message);
    this.contentUpdated$.next(event.messageId);
  }
}
