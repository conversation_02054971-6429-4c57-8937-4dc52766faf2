import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ChatSession} from './chat-session';
import {ChatSessionEvent, SessionCreatedEvent, SessionDeletedEvent, SessionUpdatedEvent} from './chat-session-event';
import {environment} from '../../../../environments/environment';
import {TokenHolder} from '../../../core/services/token-holder.service';

/**
 * 会话API请求参数
 */
export interface CreateSessionRequest {
  name: string;
}

export interface UpdateSessionRequest {
  name?: string;
  pinned?: boolean;
}

/**
 * 会话API服务
 */
@Injectable({providedIn: 'root'})
export class ChatSessionApi {
  private apiUrl = `${environment.apiBaseUrl}/sessions`;

  constructor(private http: HttpClient, private tokenHolder: TokenHolder) {
  }

  /**
   * 获取所有会话
   */
  query(): Observable<ChatSession[]> {
    return this.http.get<ChatSession[]>(this.apiUrl);
  }

  /**
   * 获取单个会话
   */
  fetch(sessionId: string): Observable<ChatSession> {
    return this.http.get<ChatSession>(`${this.apiUrl}/${sessionId}`);
  }

  /**
   * 创建新会话
   */
  create(request: CreateSessionRequest): Observable<ChatSession> {
    return this.http.post<ChatSession>(this.apiUrl, request);
  }

  /**
   * 更新会话
   */
  update(
      sessionId: string,
      request: UpdateSessionRequest,
  ): Observable<ChatSession> {
    return this.http.patch<ChatSession>(`${this.apiUrl}/${sessionId}`, request);
  }

  /**
   * 删除会话
   */
  delete(sessionId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${sessionId}`);
  }

  /**
   * 获取会话更新的事件流
   * 使用SSE (Server-Sent Events)实现实时更新
   */
  getEventStream(): Observable<ChatSessionEvent> {
    const token = this.tokenHolder.getToken();
    if (!token) {
      throw 'Token 不能为空！';
    }
    return new Observable<ChatSessionEvent>((observer) => {
      const url = `${
          environment.apiBaseUrl
      }/sessions/events?token=${encodeURIComponent(token)}`;
      const eventSource = new EventSource(url);

      // 处理会话创建事件
      eventSource.addEventListener('created', (event) => {
        try {
          const data = JSON.parse(event.data) as ChatSession;
          const createdEvent: SessionCreatedEvent = {
            type: 'created',
            sessionId: data.id,
            data: data,
          };
          observer.next(createdEvent);
        } catch (error) {
          console.error('解析会话创建事件失败', error);
        }
      });

      // 处理会话更新事件
      eventSource.addEventListener('updated', (event) => {
        try {
          const data = JSON.parse(event.data) as ChatSession;
          const updatedEvent: SessionUpdatedEvent = {
            type: 'updated',
            sessionId: data.id,
            data: data,
          };
          observer.next(updatedEvent);
        } catch (error) {
          console.error('解析会话更新事件失败', error);
        }
      });

      // 处理会话删除事件
      eventSource.addEventListener('deleted', (event) => {
        try {
          const sessionId = event.data;
          const deletedEvent: SessionDeletedEvent = {
            type: 'deleted',
            sessionId,
          };
          observer.next(deletedEvent);
        } catch (error) {
          console.error('解析会话删除事件失败', error);
        }
      });

      // 处理错误
      eventSource.onerror = (error) => {
        console.error('SSE错误', error);
        eventSource.close();
        observer.error(error);
      };

      // 清理函数
      return () => {
        eventSource.close();
      };
    });
  }
}
