import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ChatSearchResult } from './chat-search-result';

@Injectable({
  providedIn: 'root',
})
export class ChatSearchService {
  private apiUrl = `${environment.apiBaseUrl}/search`;

  constructor(private http: HttpClient) {}

  /**
   * 搜索聊天记录
   * @param query 搜索关键词
   * @returns 搜索结果列表
   */
  search(query: string): Observable<ChatSearchResult[]> {
    return this.http.get<ChatSearchResult[]>(`${this.apiUrl}/messages`, {
      params: { q: query },
    });
  }
}
