import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {SuggestionTemplate} from '../chat-suggestion-templates/chat-suggestion-templates.component';
import {environment} from '../../../../environments/environment';

/**
 * 聊天建议服务
 * 负责获取建议的后续问题和报告模板
 */
@Injectable({
  providedIn: 'root',
})
export class ChatSuggestionService {
  private apiUrl = `${environment.apiBaseUrl}/suggestions`;

  constructor(private http: HttpClient) {
  }

  /**
   * 获取建议的后续问题
   * @param sessionId 会话ID
   * @param messageId 最后一条消息ID
   * @returns 建议模板数组的Observable
   */
  getSuggestions(
      sessionId: string,
      messageId?: string,
  ): Observable<SuggestionTemplate[]> {
    return this.http.get<SuggestionTemplate[]>(
        `${this.apiUrl}?sessionId=${sessionId}${
            messageId ? `&messageId=${messageId}` : ''
        }`,
    );
  }

  /**
   * 获取报告模板
   * @param sessionId 会话ID
   * @param messageId 最后一条消息ID
   * @returns 报告模板数组的Observable
   */
  getReportTemplates(
      sessionId: string,
      messageId?: string,
  ): Observable<SuggestionTemplate[]> {
    return this.http.get<SuggestionTemplate[]>(
        `${this.apiUrl}/reports?sessionId=${sessionId}${
            messageId ? `&messageId=${messageId}` : ''
        }`,
    );
  }

  /**
   * 记录用户选择的建议问题
   * @param suggestion 用户选择的建议问题
   * @param sessionId 会话ID
   * @param messageId 消息ID
   * @returns 反馈结果的Observable
   */
  recordSuggestionUsage(
      suggestion: SuggestionTemplate,
      sessionId: string,
      messageId: string,
  ): Observable<{ success: boolean }> {
    console.log('发送建议问题反馈到:', `${this.apiUrl}/feedback`);
    return this.http.post<{ success: boolean }>(`${this.apiUrl}/feedback`, {
      suggestionId: suggestion.id,
      suggestionText: suggestion.text,
      sessionId,
      messageId,
      isReportTemplate: suggestion.isReportTemplate || false,
    });
  }
}
