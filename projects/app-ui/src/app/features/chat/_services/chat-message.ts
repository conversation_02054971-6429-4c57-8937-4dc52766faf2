import { Attachment } from './attachment';
import { ChatMessageFeedback } from './chat-message-feedback';
import { ChatMessageGenerationStep } from './chat-message-generation-step';

/**
 * 消息模型
 */
export interface ChatMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  createdAt: Date;
  isGenerating: boolean;
  steps: ChatMessageGenerationStep[]; // 过程信息，可空
  attachments: Attachment[]; // 附件数组，可空
  replyIds: string[]; // 该消息的所有回复消息ID列表，只在 role 为 'user' 时有效
  assistantName?: string; // AI 助理的名称，只在 role 为 'assistant' 时有效
  replyToId?: string; // 回复目标的消息ID
  activeReplyId?: string; // 该消息的活跃回复消息ID
  liked?: boolean; // 消息是否被点赞
  disliked?: boolean; // 消息是否被点踩
  feedback?: ChatMessageFeedback; // 点踩后的反馈内容
}
