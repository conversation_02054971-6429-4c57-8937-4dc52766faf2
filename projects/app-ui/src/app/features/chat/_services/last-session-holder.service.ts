import { Injectable } from '@angular/core';

/**
 * 会话存储服务
 * 用于管理用户最后打开的会话ID的存储和检索
 */
@Injectable({
  providedIn: 'root',
})
export class LocalStateHolder {
  private readonly LAST_SESSION_KEY = 'last_session_id';

  /**
   * 保存最后打开的会话ID
   * @param sessionId 会话ID
   */
  saveLastSessionId(sessionId: string): void {
    if (sessionId != this.getLastSessionId()) {
      localStorage.setItem(this.LAST_SESSION_KEY, sessionId);
    }
  }

  /**
   * 获取最后打开的会话ID
   * @returns 最后打开的会话ID，如果没有则返回null
   */
  getLastSessionId(): string | undefined {
    return localStorage.getItem(this.LAST_SESSION_KEY) ?? undefined;
  }

  /**
   * 清除最后打开的会话ID
   */
  clearLastSessionId(): void {
    localStorage.removeItem(this.LAST_SESSION_KEY);
  }
}
