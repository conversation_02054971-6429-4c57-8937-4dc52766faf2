import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Router, RouterModule } from '@angular/router';
import { ChatSessionComponent } from '../chat-session/chat-session.component';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ChatSessionService } from '../_services/chat-session.service';

@Component({
  selector: 'app-chat-session-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatListModule,
    MatButtonModule,
    MatDividerModule,
    MatTooltipModule,
    MatSnackBarModule,
    ChatSessionComponent,
  ],
  // 允许组件被注入
  providers: [],
  templateUrl: './chat-session-list.component.html',
  styleUrl: './chat-session-list.component.scss',
})
export class ChatSessionListComponent {
  constructor(
    private router: Router,
    private breakpointObserver: BreakpointObserver,
    protected sessions: ChatSessionService
  ) {
    // 检测是否为移动设备
    this.isMobile = this.breakpointObserver.isMatched(Breakpoints.Handset);

    // 监听断点变化
    this.breakpointObserver
      .observe([Breakpoints.Handset])
      .subscribe((result) => {
        this.isMobile = result.matches;
      });
  }

  // 是否为移动设备
  isMobile = false;

  /**
   * 创建新会话
   */
  async onCreateNewSession() {
    const session = await this.sessions.create('新会话');

    // 导航到新会话
    await this.router.navigate(['/chat', session.id]);
  }
}
