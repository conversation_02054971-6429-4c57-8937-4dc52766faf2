<div class="search-container">
  <mat-form-field appearance="outline" class="search-field">
    <div class="search-prefix" matPrefix>
      <mat-icon svgIcon="search"></mat-icon>
    </div>
    <input
      #searchInput
      matInput
      type="text"
      placeholder="搜索聊天记录..."
      [(ngModel)]="searchQuery"
      (input)="onSearchInput()"
      [matAutocomplete]="auto"
      class="search-input"
    />
    @if (searchQuery) {
      <button
        matSuffix
        mat-icon-button
        aria-label="清除"
        (click)="clearSearch()"
        matTooltip="清除搜索"
        class="clear-button"
      >
        <mat-icon svgIcon="close"></mat-icon>
      </button>
    }
  </mat-form-field>

  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onOptionSelected($event)" class="search-autocomplete">
    @if (isSearching) {
      <mat-option disabled class="status-option">
        <div class="status-message">
          <div class="loading-spinner"></div>
          <span>搜索中...</span>
        </div>
      </mat-option>
    }
    @if (!isSearching && searchResults.length === 0 && searchQuery.length >= 2) {
      <mat-option disabled class="status-option">
        <div class="status-message">
          <mat-icon svgIcon="search"></mat-icon>
          <span>未找到结果</span>
        </div>
      </mat-option>
    }
    @for (result of searchResults; track result.messageId) {
      <mat-option
        [value]="result"
        class="search-result-option"
      >
        <div class="search-result-content">
          <div class="search-result-session">{{ result.sessionName }}</div>
          <div class="search-result-message" [innerHTML]="result.snippet"></div>
        </div>
      </mat-option>
    }
  </mat-autocomplete>
</div>
