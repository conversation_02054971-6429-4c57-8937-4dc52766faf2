import {Component, ElementRef, ViewChild} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatAutocompleteModule, MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {MatTooltipModule} from '@angular/material/tooltip';
import {Router} from '@angular/router';
import {debounceTime, distinctUntilChanged, filter, switchMap} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {ChatSearchService} from '../_services/chat-search.service';
import {ChatSearchResult} from '../_services/chat-search-result';

@Component({
  selector: 'app-chat-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatAutocompleteModule,
    MatTooltipModule,
  ],
  templateUrl: './chat-search.component.html',
  styleUrl: './chat-search.component.scss',
})
export class ChatSearchComponent {
  @ViewChild('searchInput') searchInput!: ElementRef<HTMLInputElement>;

  searchQuery = '';
  searchResults: ChatSearchResult[] = [];
  isSearching = false;
  private searchSubject = new Subject<string>();

  constructor(
      private chatSearchService: ChatSearchService,
      private router: Router,
  ) {
    this.searchSubject
        .pipe(
            takeUntilDestroyed(),
            debounceTime(300),
            distinctUntilChanged(),
            filter((query) => query.length >= 2),
            switchMap((query) => {
              this.isSearching = true;
              return this.chatSearchService.search(query);
            }),
        )
        .subscribe({
          next: (results) => {
            this.searchResults = results;
            this.isSearching = false;
          },
          error: (error) => {
            console.error('搜索出错:', error);
            this.isSearching = false;
          },
        });
  }

  onSearchInput(): void {
    if (this.searchQuery.trim()) {
      this.searchSubject.next(this.searchQuery.trim());
    } else {
      this.searchResults = [];
    }
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.searchInput.nativeElement.focus();
  }

  onOptionSelected(event: MatAutocompleteSelectedEvent): void {
    const result = event.option.value as ChatSearchResult;
    this.navigateToSearchResult(result);
  }

  navigateToSearchResult(result: ChatSearchResult): void {
    // 导航到会话并滚动到消息位置
    this.router.navigate(['/chat', result.sessionId], {
      queryParams: {
        messageId: result.messageId,
        keywords: result.keywords || this.searchQuery.split(' '),
      },
    });
    this.clearSearch();
  }
}
