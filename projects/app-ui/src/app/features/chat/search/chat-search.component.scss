.search-container {
  width: 100%;
  max-width: 400px;
  margin: 0 var(--spacing-md);

  @media (max-width: 768px) {
    max-width: 100%;
    min-width: 100px;
    margin: 0 var(--spacing-xs);
  }
}

.search-field {
  width: 100%;
  font-size: var(--font-size-sm);

  /* 确保表单字段能够自适应宽度 */
  ::ng-deep .mat-mdc-form-field-flex {
    width: 100%;
  }

  ::ng-deep {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding: var(--spacing-xs) 0 !important;
      min-height: unset !important;
      width: auto !important; /* 移除默认宽度 */
    }

    .mat-mdc-text-field-wrapper {
      background-color: var(--background-color);
      border-radius: 24px;
      padding: 0 var(--spacing-xs);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--background-color);
        border-color: rgba(255, 255, 255, 0.5);
      }

      &.mdc-text-field--focused {
        background-color: var(--background-color);
        border-color: rgba(255, 255, 255, 0.6);
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
      }

      @media (max-width: 768px) {
        padding-left: var(--spacing-xxs); /* 在移动端减小左侧内边距 */
      }
    }

    .mat-mdc-form-field-flex {
      align-items: center;
      height: 36px;
    }

    .mdc-notched-outline {
      display: none;
    }
  }
}

.search-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-xs);
  color: var(--primary-color);

  mat-icon {
    font-size: 22px;
    width: 22px;
    height: 22px;
  }

  @media (max-width: 768px) {
    display: none; /* 在移动端不显示放大镜图标 */
  }
}

.search-input {
  color: var(--text-primary);
  caret-color: var(--primary-color);

  &::placeholder {
    color: var(--text-secondary);
    opacity: 1;
  }

  @media (max-width: 768px) {
    padding-left: var(--spacing-xs); /* 在移动端添加左侧内边距，补偿放大镜图标的缺失 */
  }
}

.clear-button {
  color: var(--text-secondary);
  width: 24px;
  height: 24px;
  line-height: 24px;

  &:hover {
    color: var(--primary-color);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

::ng-deep .mat-mdc-autocomplete-panel {
  border-radius: 12px;
  margin-top: 8px;
  background-color: var(--surface-color);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: var(--spacing-xs) 0;
  min-width: 300px !important;
}

.search-result-option {
  height: auto !important;
  line-height: 1.2 !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.08) !important;
  }
}

.search-result-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.search-result-session {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
    margin-right: var(--spacing-xs);
    opacity: 0.7;
  }
}

.search-result-message {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* 标准属性，提高兼容性 */
  -webkit-box-orient: vertical;
  margin-left: 16px; // 与会话名称前的圆点对齐

  ::ng-deep mark {
    background-color: var(--highlight-color);
    padding: 0 2px;
    border-radius: 2px;
    font-weight: var(--font-weight-medium);
  }
}

.status-option {
  height: 48px !important;
  line-height: 48px !important;
}

.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);

  mat-icon {
    opacity: 0.7;
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid rgba(var(--primary-rgb), 0.3);
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
