<div class="chat-input-container">
  <!-- 编辑状态指示器 -->
  @if (editingMessageId) {
    <div class="editing-indicator">
      <span>正在编辑消息</span>
      <button mat-button color="primary" (click)="cancelEditing()">取消编辑</button>
    </div>
  }
  <!-- 生成状态指示器 -->
  @if (isGenerating) {
    <div class="generating-indicator">
      <div class="pulse-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
      <span>助理正在思考中，请稍候...</span>
    </div>
  }
  <!-- 附件上传进度条 -->
  @if (isUploading && uploadProgress !== null) {
    <div class="upload-progress-container">
      <mat-progress-bar mode="determinate" [value]="uploadProgress"></mat-progress-bar>
      <span class="upload-progress-text">{{ uploadProgress }}%</span>
    </div>
  }

  <!-- 附件预览区 -->
  @if (attachments.length > 0) {
    <div class="attachments-preview">
      @for (attachment of attachments; track attachment.id; let i = $index) {
        <div class="attachment-item">
          <span class="attachment-name" [matTooltip]="attachment.name">{{ attachment.name }}</span>
          <span class="attachment-size">{{ (attachment.size / 1024).toFixed(1) }} KB</span>
          <button mat-icon-button class="remove-attachment" (click)="removeAttachment(i)">
            <span class="remove-icon">&times;</span>
          </button>
        </div>
      }
    </div>
  }

  <div class="input-actions-container">
    <!-- 隐藏的文件输入框 -->
    <input
      #fileInput
      type="file"
      style="display: none"
      (change)="onFileSelected($event)"
      accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.xlsx,.xls,.ppt,.pptx"
    >

    <!-- 消息输入框 -->
    <mat-form-field class="message-input chat-input-field" [class.disabled]="isGenerating">
        <textarea
          matInput
          placeholder="{{isGenerating ? '助理正在思考中，请稍候...' : '输入消息...'}}"
          [(ngModel)]="messageContent"
          (keydown)="onKeyDown($event)"
          rows="1"
          cdkTextareaAutosize
          #autosize="cdkTextareaAutosize"
          cdkAutosizeMinRows="1"
          cdkAutosizeMaxRows="5"
          [disabled]="isGenerating">
        </textarea>
      <mat-hint align="end">{{isGenerating ? '助理正在思考中' : '按Enter发送，Shift+Enter换行'}}</mat-hint>
    </mat-form-field>

    <div class="action-buttons-container">
      <!-- 发送按钮 -->
      <button
        mat-raised-button
        color="primary"
        class="send-button"
        [class.generating]="isGenerating"
        [disabled]="(!messageContent.trim() && attachments.length === 0) || isGenerating"
        [matTooltip]="isGenerating ? '助理正在思考中，请等待回复完成后再发送新消息' : ''"
        (click)="onSendMessage()">
        {{isGenerating ? '请等待' : '发送'}}
      </button>

      <!-- 语音输入按钮 -->
      <button
        mat-icon-button
        type="button"
        class="mic-button"
        [disabled]="isUploading || isGenerating"
        [class.active]="isListening"
        (click)="startSpeechRecognition()"
        *ngIf="speechRecognitionSupported"
        [matTooltip]="isGenerating ? '助理正在思考中，暂时无法使用语音输入' : (isListening ? '点击关闭语音输入（说“回答吧”等触发词可自动发送）' : '点击开启语音输入模式')">
        <mat-icon svgIcon="mic"></mat-icon>
      </button>

      <!-- 加号菜单按钮 -->
      <button
        mat-icon-button
        type="button"
        class="add-button"
        [matMenuTriggerFor]="addMenu"
        [disabled]="isUploading || isGenerating"
        [matTooltip]="isGenerating ? '助理正在思考中，暂时无法添加附件' : '添加附件等'">
        <mat-icon svgIcon="add"></mat-icon>
      </button>

      <!-- 加号菜单 -->
      <mat-menu #addMenu="matMenu" class="add-menu">
        <button mat-menu-item (click)="openFileSelector()">
          <mat-icon svgIcon="attachment"></mat-icon>
          <span>上传文件</span>
        </button>
        <button mat-menu-item (click)="addFavorite()">
          <mat-icon svgIcon="favorite"></mat-icon>
          <span>添加收藏</span>
        </button>
        <button mat-menu-item (click)="addLink()">
          <mat-icon svgIcon="link"></mat-icon>
          <span>引用知识库</span>
        </button>
      </mat-menu>
    </div>
  </div>
</div>
