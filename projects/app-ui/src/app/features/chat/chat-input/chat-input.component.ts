import { Component, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { Attachment } from '../_services/attachment';
import { ChatMessageService } from '../_services/chat-message.service';
import { ChatSessionService } from '../_services/chat-session.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import {SpeechRecognitionService} from '../../../shared/services/speech-recognition.service';

@Component({
  selector: 'app-chat-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TextFieldModule,
    MatProgressBarModule,
    MatTooltipModule,
    MatMenuModule,
  ],
  templateUrl: './chat-input.component.html',
  styleUrl: './chat-input.component.scss',
  providers: [
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: {
        appearance: 'fill',
        hideRequiredMarker: true,
        floatLabel: 'never',
      },
    },
  ],
})
export class ChatInputComponent {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  messageContent = '';
  attachments: Attachment[] = [];
  uploadProgress: number | null = null;
  isUploading = false;
  editingMessageId: string | null = null; // 当前正在编辑的消息ID
  isListening = false; // 是否正在进行语音识别
  speechRecognitionSupported = false; // 浏览器是否支持语音识别
  get isGenerating() {
    return this.messageService.isGenerating;
  }

  constructor(
    private messageService: ChatMessageService,
    private sessionService: ChatSessionService,
    private snackBar: MatSnackBar,
    private speechRecognitionService: SpeechRecognitionService
  ) {
    // 检查浏览器是否支持语音识别
    this.speechRecognitionSupported =
      this.speechRecognitionService.isSupported();

    // 订阅语音识别状态变化
    this.speechRecognitionService.isListening$.subscribe((isListening) => {
      this.isListening = isListening;
    });

    // 订阅编辑请求事件
    this.messageService.editRequested$.subscribe((message) => {
      this.setMessageContent(message.content, message.id, message.attachments);
    });
  }

  async onSendMessage() {
    // 如果助理消息正在生成中，则不允许发送新消息
    if (this.isGenerating) {
      this.snackBar.open(
        '助理正在思考中，请等待回复完成后再发送新消息',
        '知道了',
        {
          duration: 3000,
        }
      );
      return;
    }

    if (this.messageContent.trim() || this.attachments.length > 0) {
      try {
        const currentSession = this.sessionService.selection;
        if (!currentSession) {
          this.snackBar.open('请先选择或创建一个会话', '关闭', {
            duration: 3000,
          });
          return;
        }

        await this.messageService.send(
          currentSession.id,
          this.messageContent,
          this.attachments.length > 0 ? [...this.attachments] : undefined
        );

        // 清空输入框和附件
        this.messageContent = '';
        this.attachments = [];
        this.editingMessageId = null; // 重置编辑状态
      } catch (error) {
        console.error('发送消息失败:', error);
        this.snackBar.open('发送消息失败，请重试', '关闭', { duration: 3000 });
      }
    }
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      // 只有在助理消息不在生成中时才允许发送
      if (!this.isGenerating) {
        this.onSendMessage();
      } else {
        this.snackBar.open(
          '助理正在思考中，请等待回复完成后再发送新消息',
          '知道了',
          {
            duration: 3000,
          }
        );
      }
    }
  }

  openFileSelector() {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.uploadFile(input.files[0]);
      // 清空input，以便可以再次选择同一个文件
      input.value = '';
    }
  }

  uploadFile(file: File) {}

  removeAttachment(index: number) {
    this.attachments.splice(index, 1);
  }

  /**
   * 添加收藏
   */
  addFavorite() {
    // 模拟实现，实际项目中会调用收藏服务
    this.messageContent += '\n[收藏内容]';
  }

  /**
   * 插入链接
   */
  addLink() {
    // 模拟实现，实际项目中会打开链接输入对话框
    this.messageContent += '\n[链接]';
  }

  /**
   * 设置消息内容以进行编辑或使用模板
   * @param content 消息内容
   * @param messageId 消息ID，如果是模板则为 null
   * @param attachments 附件数组
   */
  setMessageContent(
    content: string,
    messageId: string | null,
    attachments?: Attachment[]
  ) {
    this.messageContent = content;
    this.editingMessageId = messageId;

    if (attachments && attachments.length > 0) {
      this.attachments = [...attachments];
    }
  }

  /**
   * 取消编辑
   */
  cancelEditing() {
    this.messageContent = '';
    this.attachments = [];
    this.editingMessageId = null;
  }

  /**
   * 切换语音识别状态
   */
  startSpeechRecognition() {
    if (!this.speechRecognitionSupported) {
      this.snackBar.open(
        '您的浏览器不支持语音识别功能，请使用Chrome或Edge浏览器',
        '关闭',
        {
          duration: 5000,
        }
      );
      return;
    }

    // 如果已经在监听，则停止
    if (this.isListening) {
      this.stopSpeechRecognition();
      return;
    }

    // 标记为正在监听状态
    this._startListening();

    this.snackBar.open(
      '语音输入已开启，说“回答吧”等触发词可自动发送',
      '知道了',
      {
        duration: 3000,
      }
    );
  }

  /**
   * 开始语音识别
   * 私有方法，启动持续语音识别
   */
  private _startListening() {
    // 开始语音识别
    this.speechRecognitionService.start(
      // 语音识别结果回调
      (text, isFinal) => {
        // 更新输入框内容
        if (isFinal) {
          // 如果是最终结果，累加到当前输入框内容
          if (this.messageContent) {
            // 如果已经有内容，添加空格再累加
            this.messageContent += ' ' + text;
          } else {
            // 如果输入框为空，直接设置
            this.messageContent = text;
          }
          // 持续模式下不需要重新启动，语音识别会自动继续
        } else {
          // 如果是中间结果，显示临时结果但不累加
          // 不更新this.messageContent，而是显示临时结果
          // 可以通过其他方式显示临时结果，如添加一个临时显示区域
          // 这里暂时不处理中间结果
        }
      },
      // 触发词检测回调
      (processedText) => {
        this.onSendMessage();
        // 消息发送后不停止语音识别，继续保持语音识别状态
        // 在持续模式下，语音识别会自动继续
      }
    );
  }

  /**
   * 停止语音识别
   */
  stopSpeechRecognition() {
    this.speechRecognitionService.stop();
    this.snackBar.open('语音输入已关闭', '', {
      duration: 1500,
    });
  }
}
