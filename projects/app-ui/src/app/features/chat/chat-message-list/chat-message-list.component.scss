:host {
  display: block;
  overflow-y: auto;
}

.messages-container {
  flex: 1;
  padding: 12px 12px; // 微信的内边距
  padding-bottom: 60px; // 增加底部填充，防止最后一条消息被输入框遮挡
  display: flex;
  flex-direction: column;
  gap: 16px; // 微信消息间距
  min-height: 0; // 确保在 flex 容器中可以正确收缩

  @media (max-width: 768px) {
    padding: 6px 6px; // 移动端减小内边距
    padding-bottom: 50px; // 移动端减小底部填充
    gap: 12px; // 移动端减小消息间距
  }

  .message-wrapper {
    &.highlighted-message {
      animation: highlight-pulse 2s ease-in-out;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: var(--highlight-color, rgba(255, 213, 79, 0.2));
        border-radius: 8px;
        z-index: -1;
      }
    }
  }

  /* 自定义滚动条样式 - 微信风格 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }

  // 添加微信特有的时间分隔线样式
  .message-time-divider {
    text-align: center;
    margin: 8px 0;
    font-size: 12px;
    color: #999;

    span {
      display: inline-block;
      padding: 0 10px;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999; // 微信的次要文字颜色
  font-style: normal; // 微信不使用斜体

  p {
    background-color: transparent; // 微信的提示文字没有背景
    padding: 16px;
    border-radius: 0;
    box-shadow: none; // 微信没有阴影
    border: none; // 微信没有边框
    font-size: 14px; // 微信的提示文字大小
    text-align: center; // 居中对齐
  }
}

// 折叠消息的样式
.archived-message {
  display: flex;
  justify-content: center;
  margin: 16px 0;

  .archived-message-indicator {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 16px;
    padding: 8px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;

    span {
      font-size: 13px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    button {
      font-size: 13px;
      min-width: auto;
      padding: 0 8px;
      line-height: 28px;
      height: 28px;
    }
  }
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 213, 79, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 213, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 213, 79, 0);
  }
}

.search-highlight {
  background-color: var(--highlight-color, rgba(255, 213, 79, 0.4));
  padding: 0 2px;
  border-radius: 2px;
}
