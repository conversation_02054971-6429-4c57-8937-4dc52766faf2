import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute } from '@angular/router';
import { ChatMessageComponent } from '../chat-message/chat-message.component';
import { ChatMessageService } from '../_services/chat-message.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {debounceTime, merge} from 'rxjs';
import { delay } from '../../../shared/utils/delay';

@Component({
  selector: 'app-chat-message-list',
  standalone: true,
  imports: [CommonModule, MatButtonModule, ChatMessageComponent],
  templateUrl: './chat-message-list.component.html',
  styleUrl: './chat-message-list.component.scss',
})
export class ChatMessageListComponent {
  // 用户滚动跟踪
  private userHasScrolled = false;

  // 消息容器引用
  @ViewChild('messagesContainer', { static: true })
  messagesContainer!: ElementRef<HTMLElement>;

  // 高亮的消息ID
  protected highlightedMessageId: string | null = null;

  // 高亮的搜索文本
  protected keywords: string[] = [];

  constructor(
    private el: ElementRef<HTMLElement>,
    protected messages: ChatMessageService,
    private route: ActivatedRoute
  ) {
    // 监听消息内容更新和消息加载事件
    merge(this.messages.loaded$)
      .pipe(takeUntilDestroyed(), debounceTime(100))
      .subscribe(async () => {
        await delay(100);
        this.scrollToBottom();
        this.scrollToHighlightedMessage();
      });

    // 单独处理消息内容更新事件，以便在重新生成消息时自动滚动
    this.messages.contentUpdated$
      .pipe(takeUntilDestroyed(), debounceTime(50))
      .subscribe(async (messageId) => {
        await delay(50);
        // 如果更新的消息是当前高亮的消息，则滚动到该消息
        if (messageId === this.highlightedMessageId) {
          this.scrollToHighlightedMessage();
        } else if (!this.userHasScrolled) {
          // 如果用户没有手动滚动，则滚动到底部
          this.scrollToBottom();
        }
      });
    this.messages.created$
      .pipe(takeUntilDestroyed())
      .subscribe(() => (this.userHasScrolled = false));

    // 监听高亮消息事件，用于重新生成消息时自动滚动
    this.messages.highlightMessage$
      .pipe(takeUntilDestroyed())
      .subscribe((messageId) => {
        // 清除之前的高亮
        this.clearAllHighlights();
        // 设置新的高亮消息ID
        this.highlightedMessageId = messageId;
        // 滚动到高亮消息
        this.scrollToHighlightedMessage();
      });

    // 监听URL参数变化，处理消息高亮
    this.route.queryParams.pipe(takeUntilDestroyed()).subscribe((params) => {
      // 如果参数变化，先清除所有高亮
      this.clearAllHighlights();

      // 获取新的高亮参数
      this.highlightedMessageId = params['messageId'] || null;
      this.keywords = params['keywords'] || [];

      if (this.highlightedMessageId && this.messages.messages.length > 0) {
        this.scrollToHighlightedMessage();
      }
    });
  }

  /**
   * 监听滚动事件，记录用户是否手动滚动
   */
  @HostListener('scroll', ['$event'])
  onScroll(): void {
    const element = this.el.nativeElement;
    const atBottom =
      element.scrollHeight - element.scrollTop <= element.clientHeight + 50; // 允许50px的误差

    // 如果用户滚动到底部，重置标志
    this.userHasScrolled = !atBottom;
  }

  /**
   * 滚动到消息列表底部
   * 公共方法，允许外部组件触发滚动
   * @param force 是否强制滚动，即忽略用户手动滚动状态
   */
  scrollToBottom(force = false) {
    try {
      // 如果用户手动滚动过且不强制滚动，则不滚动
      if (this.userHasScrolled && !force) {
        return;
      }
      const nativeElement = this.el.nativeElement;
      nativeElement.scrollTop = nativeElement.scrollHeight;
      // 重置用户滚动标志
      this.userHasScrolled = false;
    } catch (err) {
      console.log('滚动到底部时出错:', err);
    }
  }

  /**
   * 清除所有高亮标记
   */
  clearAllHighlights() {
    try {
      // 清除所有消息元素的高亮样式
      const highlightedElements = this.el.nativeElement.querySelectorAll(
        '.highlighted-message'
      );
      highlightedElements.forEach((element) => {
        element.classList.remove('highlighted-message');
      });

      // 清除所有文本高亮标记
      const markdownContents =
        this.el.nativeElement.querySelectorAll('.markdown-content');
      markdownContents.forEach((element) => {
        // 将所有高亮标记替换回原文本
        const marks = element.querySelectorAll('mark.search-highlight');
        marks.forEach((mark) => {
          const text = mark.textContent || '';
          const textNode = document.createTextNode(text);
          mark.parentNode?.replaceChild(textNode, mark);
        });
      });
    } catch (err) {
      console.error('清除高亮标记时出错:', err);
    }
  }

  /**
   * 在消息中高亮文本
   */
  private highlightKeywordsInMessage(
    messageElement: Element,
    keywords: string[]
  ) {
    // 查找消息内容元素
    const contentElement = messageElement.querySelector('.message-content');
    if (!contentElement) return;

    // 将文本包裹在高亮标记中
    // 注意：这是一个简化的实现，实际应用中可能需要更复杂的文本处理
    contentElement.innerHTML = keywords.reduce(
      (acc, it) =>
        acc.replace(
          new RegExp(it, 'gi'),
          (match: string) => `<mark class="search-highlight">${match}</mark>`
        ),
      contentElement.innerHTML
    );
  }

  /**
   * 滚动到高亮的消息位置
   * 如果有高亮的消息ID，则滚动到该消息的位置
   * 用于搜索结果跳转和重新生成消息时的自动滚动
   */
  scrollToHighlightedMessage(): void {
    try {
      if (!this.highlightedMessageId) return;

      // 查找高亮的消息元素
      const messageElement = this.el.nativeElement.querySelector(
        `[data-message-id="${this.highlightedMessageId}"]`
      ) as HTMLElement;

      if (messageElement) {
        // 计算滚动位置，使消息在视口中居中
        const containerHeight = this.el.nativeElement.clientHeight;
        const messageTop = messageElement.offsetTop;
        const messageHeight = messageElement.clientHeight;

        // 计算滚动位置，使消息在视口中居中
        const scrollTop = messageTop - (containerHeight / 2) + (messageHeight / 2);

        // 平滑滚动到目标位置
        this.el.nativeElement.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        });

        // 添加高亮样式
        messageElement.classList.add('highlighted-message');

        // 如果有关键词，高亮文本
        if (this.keywords && this.keywords.length > 0) {
          this.highlightKeywordsInMessage(messageElement, this.keywords);
        }
      }
    } catch (err) {
      console.error('滚动到高亮消息时出错:', err);
    }
  }
}
