<div class="messages-container" #messagesContainer>
  @if (messages.linearMessages.length === 0) {
    <div class="empty-state">
      <p>没有消息，开始新的对话吧！</p>
    </div>
  } @else {
    @for (message of messages.linearMessages; track message.id; let i = $index) {
        <div class="message-wrapper" [attr.data-message-id]="message.id"
             [class.highlighted-message]="message.id === highlightedMessageId">
          <app-chat-message [message]="message"></app-chat-message>
        </div>
    }
  }
</div>
