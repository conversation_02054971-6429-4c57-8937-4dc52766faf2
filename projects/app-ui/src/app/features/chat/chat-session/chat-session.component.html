<a mat-list-item
   [class.active]="sessions.isActive(session.id)"
   [routerLink]="['/chat', session.id]"
   (click)="sessions.selectById(session.id)">
  <mat-icon svgIcon="chat" matListItemIcon></mat-icon>
  <span matListItemTitle [matTooltip]="session.name">{{ session.name }}</span>

  <div class="action-buttons" matListItemMeta>
    <mat-icon svgIcon="pin"
              [class.pin-indicator]="session.pinned"
              [class.unpinned]="!session.pinned"
              (click)="sessions.togglePin(session)"
              [matTooltip]="getPinButtonText()"
              matTooltipPosition="above"></mat-icon>
    <mat-icon svgIcon="more" mat-icon-button [matMenuTriggerFor]="sessionMenu"
              matTooltip="更多操作"
              matTooltipPosition="above"></mat-icon>
  </div>

  <mat-menu #sessionMenu="matMenu">
    <button mat-menu-item (click)="onRename()">
      <mat-icon svgIcon="edit"></mat-icon>
      <span>重命名</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="onDelete()" class="delete-action">
      <mat-icon svgIcon="delete"></mat-icon>
      <span>删除</span>
    </button>
  </mat-menu>
</a>
