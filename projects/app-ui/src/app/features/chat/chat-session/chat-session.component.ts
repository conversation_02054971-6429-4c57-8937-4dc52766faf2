import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ChatSession } from '../_services/chat-session';
import { ChatSessionService } from '../_services/chat-session.service';
import { RenameDialogComponent } from '../rename-dialog/rename-dialog.component';
import { firstValueFrom } from 'rxjs';
import { ConfirmDialogComponent } from '../confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {MatIcon} from '@angular/material/icon';

@Component({
  selector: 'app-chat-session',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatListModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule,
    MatIcon,
  ],
  templateUrl: './chat-session.component.html',
  styleUrl: './chat-session.component.scss',
})
export class ChatSessionComponent {
  constructor(
    protected sessions: ChatSessionService,
    private dialog: MatDialog
  ) {}

  @Input() session!: ChatSession;

  /**
   * 获取置顶按钮文本
   * @returns 按钮文本
   */
  getPinButtonText(): string {
    return this.session.pinned ? '取消置顶' : '置顶';
  }

  async onRename() {
    const dialogRef = this.dialog.open(RenameDialogComponent, {
      width: '400px',
      data: { name: this.session.name },
    });

    const result = await firstValueFrom(dialogRef.afterClosed());
    if (result) {
      await this.sessions.update(this.session.id, {
        name: result.trim(),
      });
    }
  }

  async onDelete() {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: '删除会话',
        message: `确定要删除会话「${this.session.name}」吗？此操作不可撤销。`,
        confirmText: '删除',
        cancelText: '取消',
      },
    });

    const result = await firstValueFrom(dialogRef.afterClosed());
    if (result) {
      await this.sessions.delete(this.session.id);
    }
  }
}
