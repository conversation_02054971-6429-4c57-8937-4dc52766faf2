:host {
  display: block;
}

[mat-list-item] {
  --mat-list-list-item-leading-icon-start-space: var(--spacing-xs);
  --mat-list-list-item-leading-icon-end-space: var(--spacing-xs);
  border-left: var(--border-width-thick) solid transparent;
  position: relative;
  padding: 0;

  &.active {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
  }

  // 菜单按钮
  [mat-icon-button] {
    display: inline-flex;
    align-items: center;
    font-size: var(--mdc-list-list-item-leading-icon-size, 24px);
  }

  // 操作按钮区域
  .action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: 0;
    margin-right: var(--spacing-xs);

    mat-icon {
      font-size: 20px;
      height: 24px;
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.2s ease, color 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.pin-indicator {
        color: var(--primary-color);
      }

      &.unpinned {
        color: var(--text-secondary);
        opacity: 0.6;

        &:hover {
          color: var(--primary-color);
          opacity: 1;
        }
      }
    }
  }
}

// 删除按钮的特殊样式
::ng-deep {
  .delete-action {
    color: var(--error-color);
  }

  // 工具提示样式
  .mat-tooltip {
    font-size: var(--font-size-sm);
    margin-top: 4px !important;
    background-color: rgba(var(--primary-rgb), 0.9);
  }
}
