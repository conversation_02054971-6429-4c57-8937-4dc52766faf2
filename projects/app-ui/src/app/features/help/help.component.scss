:host {
  display: block;
}

.help-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

h2 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--text-secondary-color);
  padding: var(--spacing-xl) 0;
}

.help-search {
  margin-bottom: var(--spacing-xl);

  .search-field {
    width: 100%;
  }
}

.help-categories {
  margin-bottom: var(--spacing-xl);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.category-card {
  height: 100%;
  transition: box-shadow var(--transition-fast), transform var(--transition-fast), border-color var(--transition-fast);
  cursor: pointer;
  border: 2px solid transparent;

  &:hover {
    box-shadow: var(--shadow-lg);
  }

  &.selected {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.05);

    .category-icon {
      background-color: rgba(25, 118, 210, 0.2);
    }

    h3 {
      color: var(--primary-color);
    }
  }

  .category-icon {
    background-color: rgba(25, 118, 210, 0.1);
    margin-bottom: var(--spacing-md);

    .mat-icon {
      color: var(--primary-color);
    }
  }

  h3 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-md);
    color: var(--text-primary-color);
  }

  p {
    margin: 0;
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);
  }
}

.active-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: rgba(25, 118, 210, 0.05);
  border-radius: var(--border-radius-sm);

  span {
    margin-right: var(--spacing-md);
    color: var(--text-secondary-color);

    strong {
      color: var(--primary-color);
    }
  }
}

.help-faqs {
  margin-bottom: var(--spacing-xl);

  mat-expansion-panel {
    margin-bottom: var(--spacing-xs);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-xl) 0;
    color: var(--text-secondary-color);

    .mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: var(--spacing-md);
      opacity: 0.5;
    }

    p {
      margin-bottom: var(--spacing-md);
    }
  }
}

.help-contact {
  .contact-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .contact-text {
      flex: 1;

      p {
        margin: 0;
        color: var(--text-secondary-color);
      }

      .support-email {
        margin-top: var(--spacing-sm);
        font-weight: 500;
        color: var(--primary-color);
      }
    }

    .contact-actions {
      display: flex;
      gap: var(--spacing-md);

      button, a {
        .mat-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .help-contact {
    .contact-info {
      flex-direction: column;
      align-items: flex-start;

      .contact-text {
        margin-bottom: var(--spacing-md);
      }

      .contact-actions {
        width: 100%;
        flex-direction: column;

        button, a {
          width: 100%;
          margin-bottom: var(--spacing-xs);
        }
      }
    }
  }
}
