import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatTabsModule} from '@angular/material/tabs';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatBadgeModule} from '@angular/material/badge';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MessageService} from '../../api/message.service';
import {MessageResponse} from '../../core/models/message.model';

interface Message {
  id: number;
  sender?: string;
  type?: string;
  title?: string;
  avatar?: string;
  content: string;
  time: string;
  read: boolean,
}

@Component({
  selector: 'app-messages',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './messages.component.html',
  styleUrl: './messages.component.scss',
})
export class MessagesComponent implements OnInit {
  // 消息数据
  notifications: Message[] = [];

  // 私信数据
  privateMessages: Message[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  constructor(private messageService: MessageService) {
  }

  ngOnInit(): void {
    this.loadMessagesData();
  }

  /**
   * 加载消息数据
   */
  loadMessagesData(): void {
    this.loading = true;
    this.error = null;

    this.messageService.getMessages().subscribe({
      next: (data: MessageResponse) => {
        this.notifications = data.notifications || [];
        this.privateMessages = data.privateMessages || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取消息数据失败:', err);
        this.error = '获取消息数据失败，请稍后再试';
        this.loading = false;
      },
    });
  }

  // 获取未读消息数量
  getUnreadCount(messages: Message[]): number {
    return messages.filter(msg => !msg.read).length;
  }
}
