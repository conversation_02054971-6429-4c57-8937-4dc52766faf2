{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "yarn"}, "newProjectRoot": "projects", "projects": {"app-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "displayBlock": true, "standalone": true}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "root": "projects/app-ui", "sourceRoot": "projects/app-ui/src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "projects/app-ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/app-ui/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/app-ui/public"}], "styles": ["projects/app-ui/src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "app-ui:build:production"}, "development": {"buildTarget": "app-ui:build:development", "port": 4201}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "./jest.config.js"}}}}}}